import { Test, TestingModule } from '@nestjs/testing';
import { UploadsService } from './uploads.service';
import { ConfigService } from '@nestjs/config';
import { BadRequestException } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';

// Mock fs and path
jest.mock('fs', () => ({
  promises: {
    mkdir: jest.fn(),
    writeFile: jest.fn(),
    unlink: jest.fn(),
  },
  existsSync: jest.fn(),
  mkdirSync: jest.fn(), // Add this line to mock mkdirSync
}));

jest.mock('path', () => ({
  join: jest.fn(),
  extname: jest.fn(),
}));

describe('UploadsService', () => {
  let service: UploadsService;
  let configService: ConfigService;

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UploadsService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<UploadsService>(UploadsService);
    configService = module.get<ConfigService>(ConfigService);

    // Mock config values
    mockConfigService.get.mockImplementation((key) => {
      if (key === 'UPLOAD_DIR') return './uploads';
      if (key === 'MAX_FILE_SIZE') return 5 * 1024 * 1024; // 5MB
      if (key === 'ALLOWED_EXTENSIONS') return '.jpg,.jpeg,.png,.gif,.webp,.mp3,.wav,.webm';
      return null;
    });

    // Mock path.join to return a predictable path
    (path.join as jest.Mock).mockImplementation((...args) => args.join('/'));
    
    // Mock path.extname to return the file extension
    (path.extname as jest.Mock).mockImplementation((filename) => {
      const parts = filename.split('.');
      return parts.length > 1 ? `.${parts[parts.length - 1]}` : '';
    });
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('uploadFile', () => {
    it('should upload a file successfully', async () => {
      const mockFile = {
        originalname: 'test.jpg',
        buffer: Buffer.from('test file content'),
        size: 1024,
        mimetype: 'image/jpeg',
      };

      // Mock fs.existsSync to return false (directory doesn't exist)
      (fs.existsSync as jest.Mock).mockReturnValue(false);

      // Mock UUID generation
      jest.spyOn(global.Math, 'random').mockReturnValue(0.123456789);
      Date.now = jest.fn(() => 1600000000000);

      const expectedFilename = '1600000000000-0.123456789-test.jpg';
      const expectedPath = 'uploads/1600000000000-0.123456789-test.jpg';
      const expectedUrl = `/uploads/${expectedFilename}`;

      const result = await service.uploadFile(mockFile as any);

      expect(fs.promises.mkdir).toHaveBeenCalledWith('./uploads', { recursive: true });
      expect(fs.promises.writeFile).toHaveBeenCalledWith(
        expectedPath,
        mockFile.buffer
      );
      expect(result).toEqual({
        filename: expectedFilename,
        path: expectedPath,
        url: expectedUrl,
      });
    });

    it('should throw BadRequestException for files that are too large', async () => {
      const mockFile = {
        originalname: 'large.jpg',
        buffer: Buffer.from('test file content'),
        size: 10 * 1024 * 1024, // 10MB (exceeds 5MB limit)
        mimetype: 'image/jpeg',
      };

      await expect(service.uploadFile(mockFile as any)).rejects.toThrow(BadRequestException);
      expect(fs.promises.writeFile).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException for disallowed file types', async () => {
      const mockFile = {
        originalname: 'malicious.exe',
        buffer: Buffer.from('test file content'),
        size: 1024,
        mimetype: 'application/octet-stream',
      };

      await expect(service.uploadFile(mockFile as any)).rejects.toThrow(BadRequestException);
      expect(fs.promises.writeFile).not.toHaveBeenCalled();
    });
  });

  describe('uploadAudio', () => {
    it('should upload an audio file successfully', async () => {
      const mockFile = {
        originalname: 'recording.mp3',
        buffer: Buffer.from('test audio content'),
        size: 1024 * 1024, // 1MB
        mimetype: 'audio/mpeg',
      };

      // Mock fs.existsSync to return true (directory exists)
      (fs.existsSync as jest.Mock).mockReturnValue(true);

      // Mock UUID generation
      jest.spyOn(global.Math, 'random').mockReturnValue(0.987654321);
      Date.now = jest.fn(() => 1600000000000);

      const expectedFilename = '1600000000000-0.987654321-recording.mp3';
      const expectedPath = 'uploads/audio/1600000000000-0.987654321-recording.mp3';
      const expectedUrl = `/uploads/audio/${expectedFilename}`;

      // Override path.join for this specific test
      (path.join as jest.Mock).mockImplementation((...args) => {
        if (args.includes('audio')) {
          return args.join('/');
        }
        return args.join('/');
      });

      const result = await service.uploadAudio(mockFile as any);

      expect(fs.promises.mkdir).toHaveBeenCalledWith('./uploads/audio', { recursive: true });
      expect(fs.promises.writeFile).toHaveBeenCalledWith(
        expectedPath,
        mockFile.buffer
      );
      expect(result).toEqual({
        filename: expectedFilename,
        path: expectedPath,
        url: expectedUrl,
      });
    });

    it('should throw BadRequestException for non-audio files', async () => {
      const mockFile = {
        originalname: 'image.jpg',
        buffer: Buffer.from('test image content'),
        size: 1024,
        mimetype: 'image/jpeg',
      };

      await expect(service.uploadAudio(mockFile as any)).rejects.toThrow(BadRequestException);
      expect(fs.promises.writeFile).not.toHaveBeenCalled();
    });
  });

  describe('deleteFile', () => {
    it('should delete a file successfully', async () => {
      const filename = 'test.jpg';
      const filePath = 'uploads/test.jpg';

      // Mock path.join to return the expected file path
      (path.join as jest.Mock).mockReturnValue(filePath);

      // Mock fs.existsSync to return true (file exists)
      (fs.existsSync as jest.Mock).mockReturnValue(true);

      await service.deleteFile(filename);

      expect(fs.promises.unlink).toHaveBeenCalledWith(filePath);
    });

    it('should not throw if file does not exist', async () => {
      const filename = 'nonexistent.jpg';
      const filePath = 'uploads/nonexistent.jpg';

      // Mock path.join to return the expected file path
      (path.join as jest.Mock).mockReturnValue(filePath);

      // Mock fs.existsSync to return false (file doesn't exist)
      (fs.existsSync as jest.Mock).mockReturnValue(false);

      await expect(service.deleteFile(filename)).resolves.not.toThrow();
      expect(fs.promises.unlink).not.toHaveBeenCalled();
    });
  });
});
