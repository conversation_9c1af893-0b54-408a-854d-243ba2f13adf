import OpenAI from 'openai';
import { IAiProvider } from './iai-provider';
import { ChatMessage } from '../schemas/ai-chat.schema';

export class OpenAiProvider implements IAiProvider {
  private openai: OpenAI;
  private defaultModel: string;

  constructor(apiKey: string, defaultModel: string, apiBaseUrl?: string) {
    this.openai = new OpenAI({
      apiKey: apiKey,
      baseURL: apiBaseUrl, // Optional: for OpenAI compatible APIs
    });
    this.defaultModel = defaultModel;
  }

  async generateText(prompt: string, options?: { model?: string }): Promise<string> {
    try {
      const completion = await this.openai.chat.completions.create({
        model: options?.model || this.defaultModel,
        messages: [{ role: 'user', content: prompt }],
      });
      return completion.choices[0]?.message?.content || '';
    } catch (error) {
      console.error('OpenAI generateText error:', error);
      throw error; // Or handle more gracefully
    }
  }

  async createChatCompletion(messages: ChatMessage[], options?: { model?: string; temperature?: number }): Promise<ChatMessage | null> {
    try {
      // Transform ChatMessage to OpenAI's expected format if necessary (ensure 'role' and 'content' align)
      const openAiMessages = messages.map(m => ({ role: m.role as 'user' | 'assistant' | 'system', content: m.content }));

      const completion = await this.openai.chat.completions.create({
        model: options?.model || this.defaultModel,
        messages: openAiMessages,
        temperature: options?.temperature,
      });
      const responseContent = completion.choices[0]?.message?.content;
      if (!responseContent) return null;
      return {
        role: 'assistant',
        content: responseContent,
        timestamp: new Date(),
      };
    } catch (error) {
      console.error('OpenAI createChatCompletion error:', error);
      throw error;
    }
  }

  async transcribe(audioUrl: string, options?: { model?: string; language?: string }): Promise<string | null> {
    try {
      // OpenAI's transcription usually requires a file stream, not a URL directly.
      // This part might need adjustment based on how audio is handled.
      // If audioUrl is a public URL, it needs to be fetched first.
      // For now, let's assume a placeholder or that this will be refined.
      console.warn('OpenAIProvider.transcribe: Needs implementation to fetch audio from URL and stream to OpenAI API.');
      // const response = await this.openai.audio.transcriptions.create({
      //   model: options?.model || 'whisper-1', // Or another transcription model
      //   file: await fetch(audioUrl).then(res => res.blob()), // This is a simplified example
      //   language: options?.language,
      // });
      // return response.text;
      return `Transcription for ${audioUrl} (OpenAI - actual implementation pending fetching audio from URL, model: ${options?.model || 'whisper-1'}, lang: ${options?.language || 'en'})`;
    } catch (error) {
      console.error('OpenAI transcribe error:', error);
      throw error;
    }
  }
}
