# fly.toml app configuration file generated for kodiql on 2025-05-08T16:48:33-04:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'kodiql'
primary_region = 'iad'

[build]
  dockerfile = 'Dockerfile'

[env]
  NODE_ENV = 'production'
  PORT = '8080'
  HOST = '0.0.0.0'
  API_PREFIX = 'api'
  API_URL = 'https://kodiql.fly.dev/api'
  FRONTEND_URL = 'https://kodickt.fly.dev'
  NATIVE_URL = 'exp://kodi-native.fly.dev'

  AWS_REGION="auto"
  MAIL_PROVIDER="smtp"
  MAIL_HOST="chi204.greengeeks.net"
  MAIL_PORT=465
  MAIL_SECURE=true
  MAIL_FROM_NAME="Kodi Support"
  MAIL_FROM_ADDRESS="<EMAIL>"


# Volume for temporary audio uploads
# Most files are stored in AWS S3, but audio uploads use local storage temporarily
[[mounts]]
  source = 'kodi_nest_data'
  destination = '/app/uploads'

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

  [http_service.concurrency]
    type = 'connections'
    hard_limit = 1000
    soft_limit = 500

[[vm]]
  cpu_kind = 'shared'
  cpus = 1
  memory_mb = 1024
