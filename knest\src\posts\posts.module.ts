import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { PostsService } from './posts.service';
import { PostsController } from './posts.controller';
import { Post, PostSchema } from './schemas/post.schema';
import { PostType, PostTypeSchema } from './schemas/post-type.schema';
import { Tag, TagSchema } from './schemas/tag.schema';
import { Comment, CommentSchema } from './schemas/comment.schema';
import { CompaniesModule } from '../companies/companies.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Post.name, schema: PostSchema },
      { name: PostType.name, schema: PostTypeSchema },
      { name: Tag.name, schema: TagSchema },
      { name: Comment.name, schema: CommentSchema },
    ]),
    forwardRef(() => CompaniesModule),
  ],
  controllers: [PostsController],
  providers: [PostsService],
  exports: [PostsService],
})
export class PostsModule {}
