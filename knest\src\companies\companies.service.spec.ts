import { Test, TestingModule } from '@nestjs/testing';
import { CompaniesService } from './companies.service';
import { getModelToken } from '@nestjs/mongoose';
import { Company } from './schemas/company.schema';
import { ModuleRef } from '@nestjs/core';
import { NotFoundException } from '@nestjs/common';
import { Model } from 'mongoose';

describe('CompaniesService', () => {
  let service: CompaniesService;
  let companyModel: Model<Company>;
  let moduleRef: ModuleRef;

  // Mock constructor for companyModel
  const mockCompanyModelStatics = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
    create: jest.fn(),
  };
  function MockCompanyModelConstructor(dto) {
    return {
      ...dto,
      save: jest.fn().mockResolvedValue({ _id: 'company1', ...dto }),
    };
  }
  Object.assign(MockCompanyModelConstructor, mockCompanyModelStatics);

  let mockPostsService: any;
  let mockTeamsService: any;
  let mockModuleRefGet: jest.Mock;

  // Mock constructor for companyModel
  const mockCompanyModelStatics = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
    create: jest.fn(),
  };

  function MockCompanyModelConstructor(dto) {
    return {
      ...dto,
      save: jest.fn().mockResolvedValue({ _id: 'company1', ...dto }), // Ensure _id is part of the resolved value
    };
  }
  Object.assign(MockCompanyModelConstructor, mockCompanyModelStatics);


  beforeEach(async () => {
    jest.clearAllMocks();
    global.console.warn = jest.fn(); // Mock console.warn
    global.console.error = jest.fn(); // Mock console.error for error handling tests

    mockPostsService = {
      findAllPostTypes: jest.fn(),
      createPostType: jest.fn(),
    };

    mockTeamsService = {
      findByCompany: jest.fn(),
      create: jest.fn(),
    };

    mockModuleRefGet = jest.fn((token: any) => {
      if (token === 'PostsService') { // Using string token as per actual code
        return mockPostsService;
      }
      if (token === 'TeamsService') { // Using string token as per actual code
        return mockTeamsService;
      }
      return undefined;
    });

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CompaniesService,
        {
          provide: getModelToken(Company.name),
          useValue: MockCompanyModelConstructor as any,
        },
        {
          provide: ModuleRef,
          useValue: { get: mockModuleRefGet }, // Use the mock function here
        },
      ],
    }).compile();

    service = module.get<CompaniesService>(CompaniesService);
    companyModel = module.get<Model<Company>>(getModelToken(Company.name));
    moduleRef = module.get<ModuleRef>(ModuleRef); // This will get the mocked ModuleRef

    jest.spyOn(service as any, 'generateInviteCode').mockReturnValue('abc12345');
    
    // Call onModuleInit to set up postsService and teamsService
    // We need to ensure moduleRef.get is called with the correct string tokens
    // The actual implementation uses require('../posts/posts.service').PostsService
    // For testing, we'll simulate this by having our mockModuleRef.get respond to 'PostsService' and 'TeamsService'
    service.onModuleInit(); 
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a company with a generated invite code', async () => {
      const createCompanyDto = {
        name: 'Test Company',
        createdBy: 'user1',
      };

      const savedCompany = {
        _id: 'company1',
        ...createCompanyDto,
        invite_code: 'abc12345',
      };

      // Mock the createDefaultPostTypes method
      const spy = jest.spyOn(service, 'createDefaultPostTypes').mockResolvedValue(undefined);

      const result = await service.create(createCompanyDto);

      // Debug: log the calls
      // eslint-disable-next-line no-console
      console.log('createDefaultPostTypes calls:', spy.mock.calls);

      expect(service['generateInviteCode']).toHaveBeenCalled();
      expect(service.createDefaultPostTypes).toHaveBeenCalledWith('company1', 'user1');
      expect(result).toEqual(savedCompany);
    });

    it('should use provided invite code if available', async () => {
      const createCompanyDto = {
        name: 'Test Company',
        createdBy: 'user1',
        invite_code: 'custom123',
      };

      const savedCompany = {
        _id: 'company1',
        ...createCompanyDto,
      };

      // Mock the createDefaultPostTypes method
      const spy2 = jest.spyOn(service, 'createDefaultPostTypes').mockResolvedValue(undefined);

      const result = await service.create(createCompanyDto);

      // Debug: log the calls
      // eslint-disable-next-line no-console
      console.log('createDefaultPostTypes calls:', spy2.mock.calls);

      expect(service['generateInviteCode']).not.toHaveBeenCalled();
      expect(service.createDefaultPostTypes).toHaveBeenCalledWith('company1', 'user1');
      expect(result).toEqual(savedCompany);
    });
  });

  describe('findAll', () => {
    it('should return all companies', async () => {
      const mockCompanies = [
        { _id: 'company1', name: 'Company 1' },
        { _id: 'company2', name: 'Company 2' },
      ];

      (companyModel as any).find.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockCompanies),
      });

      const result = await service.findAll();

      expect(companyModel.find).toHaveBeenCalled();
      expect(result).toEqual(mockCompanies);
    });
  });

  describe('findById', () => {
    it('should return a company when it exists', async () => {
      const mockCompany = { _id: 'company1', name: 'Test Company' };

      (companyModel as any).findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockCompany),
      });

      const result = await service.findById('company1');

      expect(companyModel.findById).toHaveBeenCalledWith('company1');
      expect(result).toEqual(mockCompany);
    });

    it('should throw NotFoundException when company does not exist', async () => {
      (companyModel as any).findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.findById('nonexistent')).rejects.toThrow(NotFoundException);
      expect(companyModel.findById).toHaveBeenCalledWith('nonexistent');
    });
  });

  describe('findByInviteCode', () => {
    it('should return a company when invite code exists', async () => {
      const mockCompany = { _id: 'company1', name: 'Test Company', invite_code: 'abc123' };

      (companyModel as any).findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockCompany),
      });

      const result = await service.findByInviteCode('abc123');

      expect(companyModel.findOne).toHaveBeenCalledWith({ invite_code: 'abc123' });
      expect(result).toEqual(mockCompany);
    });

    it('should return null when invite code does not exist', async () => {
      (companyModel as any).findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      const result = await service.findByInviteCode('nonexistent');

      expect(companyModel.findOne).toHaveBeenCalledWith({ invite_code: 'nonexistent' });
      expect(result).toBeNull();
    });
  });

  describe('update', () => {
    it('should update a company when it exists', async () => {
      const updateCompanyDto = {
        name: 'Updated Company',
      };

      const updatedCompany = {
        _id: 'company1',
        name: 'Updated Company',
        invite_code: 'abc123',
      };

      (companyModel as any).findByIdAndUpdate.mockReturnValue({
        exec: jest.fn().mockResolvedValue(updatedCompany),
      });

      const result = await service.update('company1', updateCompanyDto);

      expect(companyModel.findByIdAndUpdate).toHaveBeenCalledWith('company1', updateCompanyDto, { new: true });
      expect(result).toEqual(updatedCompany);
    });

    it('should throw NotFoundException when company does not exist', async () => {
      (companyModel as any).findByIdAndUpdate.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.update('nonexistent', { name: 'Updated' })).rejects.toThrow(NotFoundException);
      expect(companyModel.findByIdAndUpdate).toHaveBeenCalledWith('nonexistent', { name: 'Updated' }, { new: true });
    });
  });

  describe('remove', () => {
    it('should remove a company when it exists', async () => {
      const deletedCompany = { _id: 'company1', name: 'Test Company' };

      (companyModel as any).findByIdAndDelete.mockReturnValue({
        exec: jest.fn().mockResolvedValue(deletedCompany),
      });

      const result = await service.remove('company1');

      expect(companyModel.findByIdAndDelete).toHaveBeenCalledWith('company1');
      expect(result).toEqual(deletedCompany);
    });

    it('should throw NotFoundException when company does not exist', async () => {
      (companyModel as any).findByIdAndDelete.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.remove('nonexistent')).rejects.toThrow(NotFoundException);
      expect(companyModel.findByIdAndDelete).toHaveBeenCalledWith('nonexistent');
    });
  });

  // Remove the old basic test for createDefaultPostTypes

  describe('createDefaultPostTypes', () => {
    const companyId = 'newCompanyId';
    const userId = 'creatingUserId';
    const templateCompanyId = '682f32c984a2f20bec505d5e';

    it('should successfully copy post types from template', async () => {
      const mockTemplatePostTypes = [
        { name: 'Type 1', description: 'Desc 1', recordTips: 'Tips 1', highlightColor: '#FF0000', archived: false },
        { name: 'Type 2', description: 'Desc 2', recordTips: 'Tips 2', highlightColor: '#00FF00', archived: true }, // Should be skipped
        { name: 'Type 3', description: 'Desc 3', recordTips: 'Tips 3', highlightColor: '#0000FF', archived: false },
      ];
      mockPostsService.findAllPostTypes.mockResolvedValue(mockTemplatePostTypes);

      await service.createDefaultPostTypes(companyId, userId);

      expect(mockPostsService.findAllPostTypes).toHaveBeenCalledWith(templateCompanyId);
      expect(mockPostsService.createPostType).toHaveBeenCalledTimes(2); // Type 2 is archived
      expect(mockPostsService.createPostType).toHaveBeenNthCalledWith(1, {
        name: 'Type 1',
        description: 'Desc 1',
        recordTips: 'Tips 1',
        highlightColor: '#FF0000',
        companyId: companyId,
        userId: userId,
        archived: false,
      });
      expect(mockPostsService.createPostType).toHaveBeenNthCalledWith(2, {
        name: 'Type 3',
        description: 'Desc 3',
        recordTips: 'Tips 3',
        highlightColor: '#0000FF',
        companyId: companyId,
        userId: userId,
        archived: false,
      });
    });

    it('should not call createPostType if PostsService is not available', async () => {
      (service as any).postsService = undefined; // Simulate service not available
      await service.createDefaultPostTypes(companyId, userId);

      expect(mockPostsService.findAllPostTypes).not.toHaveBeenCalled();
      expect(mockPostsService.createPostType).not.toHaveBeenCalled();
      expect(console.warn).toHaveBeenCalledWith('PostsService not available, skipping creation of default post types.');
    });

    it('should not call createPostType if template company has no post types', async () => {
      mockPostsService.findAllPostTypes.mockResolvedValue([]);
      await service.createDefaultPostTypes(companyId, userId);

      expect(mockPostsService.findAllPostTypes).toHaveBeenCalledWith(templateCompanyId);
      expect(mockPostsService.createPostType).not.toHaveBeenCalled();
      expect(console.warn).toHaveBeenCalledWith(`No post types found for template company ${templateCompanyId}. Skipping default post type creation.`);
    });
     it('should handle errors during findAllPostTypes', async () => {
      mockPostsService.findAllPostTypes.mockRejectedValue(new Error('DB error'));
      await service.createDefaultPostTypes(companyId, userId);
      expect(mockPostsService.createPostType).not.toHaveBeenCalled();
      expect(console.error).toHaveBeenCalledWith('Error creating default post types:', 'DB error');
    });

    it('should handle errors during createPostType', async () => {
      const mockTemplatePostTypes = [{ name: 'Type 1', archived: false }];
      mockPostsService.findAllPostTypes.mockResolvedValue(mockTemplatePostTypes);
      mockPostsService.createPostType.mockRejectedValue(new Error('Create error'));
      
      await service.createDefaultPostTypes(companyId, userId);
      
      expect(mockPostsService.createPostType).toHaveBeenCalledTimes(1);
      expect(console.error).toHaveBeenCalledWith('Error creating default post types:', 'Create error');
    });
  });

  describe('createDefaultTeams', () => {
    const companyId = 'newCompanyId';
    const userId = 'creatingUserId';
    const templateCompanyId = '682f32c984a2f20bec505d5e';

    it('should successfully copy teams from template', async () => {
      const mockTemplateTeams = [
        { name: 'Team A', description: 'Desc A', highlightColor: '#FFFF00' },
        { name: 'Team B', description: 'Desc B', highlightColor: '#FF00FF' },
      ];
      mockTeamsService.findByCompany.mockResolvedValue(mockTemplateTeams);

      await service.createDefaultTeams(companyId, userId);

      expect(mockTeamsService.findByCompany).toHaveBeenCalledWith(templateCompanyId);
      expect(mockTeamsService.create).toHaveBeenCalledTimes(2);
      expect(mockTeamsService.create).toHaveBeenNthCalledWith(1, {
        name: 'Team A',
        description: 'Desc A',
        highlightColor: '#FFFF00',
        companyId: companyId,
        userId: userId,
      }, userId);
      expect(mockTeamsService.create).toHaveBeenNthCalledWith(2, {
        name: 'Team B',
        description: 'Desc B',
        highlightColor: '#FF00FF',
        companyId: companyId,
        userId: userId,
      }, userId);
    });

    it('should not call create if TeamsService is not available', async () => {
      (service as any).teamsService = undefined; // Simulate service not available
      await service.createDefaultTeams(companyId, userId);

      expect(mockTeamsService.findByCompany).not.toHaveBeenCalled();
      expect(mockTeamsService.create).not.toHaveBeenCalled();
      expect(console.warn).toHaveBeenCalledWith('TeamsService not available, skipping creation of default teams.');
    });

    it('should not call create if template company has no teams', async () => {
      mockTeamsService.findByCompany.mockResolvedValue([]);
      await service.createDefaultTeams(companyId, userId);

      expect(mockTeamsService.findByCompany).toHaveBeenCalledWith(templateCompanyId);
      expect(mockTeamsService.create).not.toHaveBeenCalled();
      expect(console.warn).toHaveBeenCalledWith(`No teams found for template company ${templateCompanyId}. Skipping default team creation.`);
    });
    
    it('should handle errors during findByCompany', async () => {
      mockTeamsService.findByCompany.mockRejectedValue(new Error('DB error teams'));
      await service.createDefaultTeams(companyId, userId);
      expect(mockTeamsService.create).not.toHaveBeenCalled();
      expect(console.error).toHaveBeenCalledWith('Error creating default teams:', 'DB error teams');
    });

    it('should handle errors during teamsService.create', async () => {
      const mockTemplateTeams = [{ name: 'Team A' }];
      mockTeamsService.findByCompany.mockResolvedValue(mockTemplateTeams);
      mockTeamsService.create.mockRejectedValue(new Error('Create team error'));
      
      await service.createDefaultTeams(companyId, userId);
      
      expect(mockTeamsService.create).toHaveBeenCalledTimes(1);
      expect(console.error).toHaveBeenCalledWith('Error creating default teams:', 'Create team error');
    });
  });
});
