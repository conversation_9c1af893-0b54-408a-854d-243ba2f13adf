import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ObjectId } from 'mongoose';
import { Team, TeamDocument } from './schemas/team.schema';
import { Membership, MembershipDocument } from './schemas/membership.schema';
import { CreateTeamDto } from './dto/create-team.dto';
import { UpdateTeamDto } from './dto/update-team.dto';
import { CreateMembershipDto } from './dto/create-membership.dto';

@Injectable()
export class TeamsService {
  constructor(
    @InjectModel(Team.name) private teamModel: Model<TeamDocument>,
    @InjectModel(Membership.name)
    private membershipModel: Model<MembershipDocument>,
  ) {}

  async create(createTeamDto: CreateTeamDto): Promise<TeamDocument> {
    const newTeam = new this.teamModel(createTeamDto);
    const team = await newTeam.save();

    // Add the creator as a member
    await this.addMember({
      teamId: (team._id as ObjectId).toString(),
      memberId: createTeamDto.userId,
    });

    return team;
  }

  async findAll(): Promise<TeamDocument[]> {
    return this.teamModel.find().exec();
  }

  async findByCompany(companyId: string): Promise<TeamDocument[]> {
    return this.teamModel.find({ companyId }).exec();
  }

  async findById(id: string): Promise<TeamDocument> {
    const team = await this.teamModel.findById(id).exec();
    if (!team) {
      throw new NotFoundException(`Team with ID ${id} not found`);
    }
    return team;
  }

  async update(
    id: string,
    updateTeamDto: UpdateTeamDto,
  ): Promise<TeamDocument> {
    const updatedTeam = await this.teamModel
      .findByIdAndUpdate(id, updateTeamDto, { new: true })
      .exec();

    if (!updatedTeam) {
      throw new NotFoundException(`Team with ID ${id} not found`);
    }

    return updatedTeam;
  }

  async remove(id: string): Promise<TeamDocument> {
    // First, delete all memberships for this team
    await this.membershipModel.deleteMany({ teamId: id }).exec();

    // Then delete the team
    const deletedTeam = await this.teamModel.findByIdAndDelete(id).exec();

    if (!deletedTeam) {
      throw new NotFoundException(`Team with ID ${id} not found`);
    }

    return deletedTeam;
  }

  async getTeamMembers(teamId: string): Promise<MembershipDocument[]> {
    return this.membershipModel.find({ teamId }).populate('memberId').exec();
  }

  async getUserTeams(userId: string, companyId?: string): Promise<TeamDocument[]> {
    const memberships = await this.membershipModel
      .find({ memberId: userId })
      .exec();
    const teamIds = memberships.map((membership) => membership.teamId);

    // If companyId is provided, filter teams by both team IDs and company ID
    const query: any = { _id: { $in: teamIds } };
    if (companyId) {
      query.companyId = companyId;
    }

    return this.teamModel.find(query).exec();
  }

  async addMember(
    createMembershipDto: CreateMembershipDto,
  ): Promise<MembershipDocument> {
    // Check if team exists
    const team = await this.teamModel
      .findById(createMembershipDto.teamId)
      .exec();
    if (!team) {
      throw new NotFoundException(
        `Team with ID ${createMembershipDto.teamId} not found`,
      );
    }

    // Check if membership already exists
    const existingMembership = await this.membershipModel
      .findOne({
        teamId: createMembershipDto.teamId,
        memberId: createMembershipDto.memberId,
      })
      .exec();

    if (existingMembership) {
      throw new BadRequestException('User is already a member of this team');
    }

    const newMembership = new this.membershipModel(createMembershipDto);
    return newMembership.save();
  }

  async removeMember(teamId: string, memberId: string): Promise<void> {
    const result = await this.membershipModel
      .deleteOne({
        teamId,
        memberId,
      })
      .exec();

    if (result.deletedCount === 0) {
      throw new NotFoundException(
        `Membership not found for team ${teamId} and member ${memberId}`,
      );
    }
  }

  async isMember(teamId: string, userId: string): Promise<boolean> {
    const membership = await this.membershipModel
      .findOne({
        teamId,
        memberId: userId,
      })
      .exec();

    return !!membership;
  }
}
