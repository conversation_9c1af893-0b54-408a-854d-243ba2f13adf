import { Test, TestingModule } from '@nestjs/testing';
import { ProfilesService } from './profiles.service';
import { getModelToken } from '@nestjs/mongoose';
import { Profile } from './schemas/profile.schema';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { Model } from 'mongoose';

describe('ProfilesService', () => {
  let service: ProfilesService;
  let profileModel: Model<Profile>;

  // Mock for constructor
  const mockProfileModelStatics = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
  };
  // The constructor function
  function mockProfileModelConstructor(dto) {
    return {
      ...dto,
      save: jest.fn().mockResolvedValue({ _id: 'profile1', ...dto }),
    };
  }
  // Attach static methods to the constructor
  Object.assign(mockProfileModelConstructor, mockProfileModelStatics);

  // Instead of patching the injected model, directly use a plain object for the model mock and cast it as any when injecting
  const mockProfileModel = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
  };
  function MockProfileModelConstructor(dto) {
    return {
      ...dto,
      save: jest.fn().mockResolvedValue({ _id: 'profile1', ...dto }),
    };
  }
  Object.assign(MockProfileModelConstructor, mockProfileModel);

  beforeEach(async () => {
    jest.clearAllMocks();
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProfilesService,
        {
          provide: getModelToken(Profile.name),
          useValue: MockProfileModelConstructor as any,
        },
      ],
    }).compile();
    service = module.get<ProfilesService>(ProfilesService);
    profileModel = module.get<Model<Profile>>(getModelToken(Profile.name));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new profile', async () => {
      const createProfileDto = {
        userId: 'user1',
        firstName: 'Test',
        lastName: 'User',
        companyId: 'company1',
      };
      const savedProfile = {
        _id: 'profile1',
        ...createProfileDto,
      };
      // The constructor is already mocked globally, so just call create
      const result = await service.create(createProfileDto);
      expect(result).toEqual(savedProfile);
    });
  });

  describe('findAll', () => {
    it('should return all profiles', async () => {
      const mockProfiles = [
        { _id: 'profile1', firstName: 'Test1', lastName: 'User1' },
        { _id: 'profile2', firstName: 'Test2', lastName: 'User2' },
      ];

      (profileModel.find as any).mockImplementation(() => ({
        exec: jest.fn().mockResolvedValue(mockProfiles),
      }));

      const result = await service.findAll();

      expect(profileModel.find).toHaveBeenCalled();
      expect(result).toEqual(mockProfiles);
    });
  });

  describe('findByCompany', () => {
    it('should return profiles for a specific company', async () => {
      const mockProfiles = [
        { _id: 'profile1', firstName: 'Test1', lastName: 'User1', companyId: 'company1' },
      ];

      (profileModel.find as any).mockImplementation(() => ({
        exec: jest.fn().mockResolvedValue(mockProfiles),
      }));

      const result = await service.findByCompany('company1');

      expect(profileModel.find).toHaveBeenCalledWith({ companyId: 'company1' });
      expect(result).toEqual(mockProfiles);
    });
  });

  describe('findById', () => {
    it('should return a profile when it exists', async () => {
      const mockProfile = { _id: 'profile1', firstName: 'Test', lastName: 'User' };

      (profileModel.findById as any).mockImplementation(() => ({
        exec: jest.fn().mockResolvedValue(mockProfile),
      }));

      const result = await service.findById('profile1');

      expect(profileModel.findById).toHaveBeenCalledWith('profile1');
      expect(result).toEqual(mockProfile);
    });

    it('should throw NotFoundException when profile does not exist', async () => {
      (profileModel.findById as any).mockImplementation(() => ({
        exec: jest.fn().mockResolvedValue(null),
      }));

      await expect(service.findById('nonexistent')).rejects.toThrow(NotFoundException);
      expect(profileModel.findById).toHaveBeenCalledWith('nonexistent');
    });
  });

  describe('findByUserId', () => {
    it('should return a profile when user exists', async () => {
      const mockProfile = { _id: 'profile1', userId: 'user1', firstName: 'Test', lastName: 'User' };
      (profileModel.findOne as any).mockImplementation(() => ({
        exec: jest.fn().mockResolvedValue(mockProfile),
      }));
      const result = await service.findByUserId('user1');
      expect(profileModel.findOne).toHaveBeenCalledWith({ userId: 'user1' });
      expect(result).toEqual(mockProfile);
    });
    it('should throw NotFoundException when user does not have a profile', async () => {
      (profileModel.findOne as any).mockImplementation(() => ({
        exec: jest.fn().mockResolvedValue(null),
      }));
      await expect(service.findByUserId('nonexistent')).rejects.toThrow(NotFoundException);
      expect(profileModel.findOne).toHaveBeenCalledWith({ userId: 'nonexistent' });
    });
  });

  describe('update', () => {
    it('should update a profile when it exists', async () => {
      const updateProfileDto = {
        firstName: 'Updated',
        lastName: 'User',
      };

      const updatedProfile = {
        _id: 'profile1',
        userId: 'user1',
        ...updateProfileDto,
      };

      (profileModel.findByIdAndUpdate as any).mockImplementation(() => ({
        exec: jest.fn().mockResolvedValue(updatedProfile),
      }));

      const result = await service.update('profile1', updateProfileDto);

      expect(profileModel.findByIdAndUpdate).toHaveBeenCalledWith('profile1', updateProfileDto, { new: true });
      expect(result).toEqual(updatedProfile);
    });

    it('should throw NotFoundException when profile does not exist', async () => {
      (profileModel.findByIdAndUpdate as any).mockImplementation(() => ({
        exec: jest.fn().mockResolvedValue(null),
      }));

      await expect(service.update('nonexistent', { firstName: 'Updated' })).rejects.toThrow(NotFoundException);
      expect(profileModel.findByIdAndUpdate).toHaveBeenCalledWith('nonexistent', { firstName: 'Updated' }, { new: true });
    });
  });

  describe('updateUserSettings', () => {
    it('should update user settings when profile exists', async () => {
      const existingProfile = {
        _id: 'profile1',
        userId: 'user1',
        firstName: 'Test',
        lastName: 'User',
        userSettings: {
          showOnboarder: true,
        },
        save: jest.fn().mockResolvedValue({
          _id: 'profile1',
          userId: 'user1',
          firstName: 'Test',
          lastName: 'User',
          userSettings: { showOnboarder: false },
        }),
      };
      (profileModel.findOne as any).mockImplementation(() => ({
        exec: jest.fn().mockResolvedValue(existingProfile),
      }));
      const result = await service.updateUserSettings('user1', { showOnboarder: false });
      expect(profileModel.findOne).toHaveBeenCalledWith({ userId: 'user1' });
      expect(result).toEqual({
        _id: 'profile1',
        userId: 'user1',
        firstName: 'Test',
        lastName: 'User',
        userSettings: { showOnboarder: false },
      });
    });
    it('should throw NotFoundException when profile does not exist', async () => {
      (profileModel.findOne as any).mockImplementation(() => ({
        exec: jest.fn().mockResolvedValue(null),
      }));
      await expect(service.updateUserSettings('nonexistent', { showOnboarder: false })).rejects.toThrow(NotFoundException);
      expect(profileModel.findOne).toHaveBeenCalledWith({ userId: 'nonexistent' });
    });
  });

  describe('remove', () => {
    it('should remove a profile when it exists', async () => {
      const deletedProfile = { _id: 'profile1', firstName: 'Test', lastName: 'User' };

      (profileModel.findByIdAndDelete as any).mockImplementation(() => ({
        exec: jest.fn().mockResolvedValue(deletedProfile),
      }));

      const result = await service.remove('profile1');

      expect(profileModel.findByIdAndDelete).toHaveBeenCalledWith('profile1');
      expect(result).toEqual(deletedProfile);
    });

    it('should throw NotFoundException when profile does not exist', async () => {
      (profileModel.findByIdAndDelete as any).mockImplementation(() => ({
        exec: jest.fn().mockResolvedValue(null),
      }));

      await expect(service.remove('nonexistent')).rejects.toThrow(NotFoundException);
      expect(profileModel.findByIdAndDelete).toHaveBeenCalledWith('nonexistent');
    });
  });
});
