import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
}

export type AiChatDocument = AiChat & Document;

@Schema({
  collection: 'kd_ai_chats',
  timestamps: true,
})
export class AiChat {
  @Prop({ required: true })
  title: string;

  @Prop({ type: [Object], default: [] })
  messages: ChatMessage[];

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true, index: true })
  userId: MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Company', required: false, index: true })
  companyId: MongooseSchema.Types.ObjectId;
}

export const AiChatSchema = SchemaFactory.createForClass(AiChat);
