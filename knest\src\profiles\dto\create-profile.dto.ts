import { IsNotEmpty, IsString, IsOptional, IsObject, IsArray } from 'class-validator';

export class CreateProfileDto {
  @IsNotEmpty()
  @IsString()
  userId: string;

  @IsOptional()
  @IsString()
  firstName?: string;

  @IsOptional()
  @IsString()
  lastName?: string;

  @IsOptional()
  @IsString()
  role?: string;

  @IsOptional()
  @IsString()
  companyId?: string;

  @IsOptional()
  @IsString()
  avatar?: string;

  @IsOptional()
  @IsObject()
  transformedAvatar?: {
    small: string;
    medium: string;
    large: string;
  };

  @IsOptional()
  @IsArray()
  hashtags?: string[];

  @IsOptional()
  @IsObject()
  userSettings?: {
    showOnboarder: boolean;
    onboarderStep: number;
    [key: string]: any;
  };
}
