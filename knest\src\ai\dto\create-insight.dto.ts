import { IsNotEmpty, IsString, IsOptional, IsObject } from 'class-validator';

export class CreateInsightDto {
  @IsNotEmpty()
  @IsString()
  title: string;

  @IsOptional()
  @IsString()
  tag?: string;

  @IsOptional()
  @IsString()
  companyId?: string;

  @IsOptional()
  @IsString()
  touchPointName?: string;

  @IsOptional()
  @IsObject()
  touchPointSource?: any;

  @IsOptional()
  @IsString()
  touchPointText?: string;

  @IsOptional()
  @IsString()
  summary?: string;

  @IsNotEmpty()
  @IsString()
  userId: string;

  @IsOptional()
  @IsString()
  postId?: string; // Added for linking insight to a specific post

  // Fields for the new analyzePost method, if not already covered
  @IsOptional()
  @IsString()
  linkedObjectId?: string;

  @IsOptional()
  @IsString()
  objectType?: string; // e.g., 'Post'

  @IsOptional()
  @IsString()
  prompt?: string; // To store the prompt used for generation
}
