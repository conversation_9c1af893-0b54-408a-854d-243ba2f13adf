import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query } from '@nestjs/common';
import { AiService } from './ai.service';
import { CreateSummaryDto } from './dto/create-summary.dto';
import { CreateInsightDto } from './dto/create-insight.dto';
import { CreateAiPromptDto } from './dto/create-ai-prompt.dto';
import { CreateAiChatDto } from './dto/create-ai-chat.dto';
import { CreateAppSettingsDto } from './dto/create-app-settings.dto';
import { TranscribeAudioDto } from './dto/transcribe-audio.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';

@Controller('ai')
export class AiController {
  constructor(private readonly aiService: AiService) {}

  // Summary endpoints
  @UseGuards(JwtAuthGuard)
  @Post('summaries')
  createSummary(@Body() createSummaryDto: CreateSummaryDto) {
    return this.aiService.createSummary(createSummaryDto);
  }

  @UseGuards(JwtAuthGuard)
  @Get('summaries')
  findAllSummaries(
    @Query('userId') userId?: string,
    @Query('companyId') companyId?: string,
    @Query('tag') tag?: string,
  ) {
    return this.aiService.findAllSummaries(userId, companyId, tag);
  }

  @UseGuards(JwtAuthGuard)
  @Get('summaries/:id')
  findSummaryById(@Param('id') id: string) {
    return this.aiService.findSummaryById(id);
  }

  @UseGuards(JwtAuthGuard)
  @Patch('summaries/:id')
  updateSummary(
    @Param('id') id: string,
    @Body() updateSummaryDto: any,
  ) {
    return this.aiService.updateSummary(id, updateSummaryDto);
  }

  @UseGuards(JwtAuthGuard)
  @Delete('summaries/:id')
  removeSummary(@Param('id') id: string) {
    return this.aiService.removeSummary(id);
  }

  @UseGuards(JwtAuthGuard)
  @Post('summaries/generate')
  generateSummary(@Body() createSummaryDto: CreateSummaryDto) {
    return this.aiService.generateSummary(createSummaryDto);
  }

  // Insight endpoints
  @UseGuards(JwtAuthGuard)
  @Post('insights')
  createInsight(@Body() createInsightDto: CreateInsightDto) {
    return this.aiService.createInsight(createInsightDto);
  }

  @UseGuards(JwtAuthGuard)
  @Get('insights')
  findAllInsights(
    @Query('userId') userId?: string,
    @Query('companyId') companyId?: string,
    @Query('tag') tag?: string,
  ) {
    return this.aiService.findAllInsights(userId, companyId, tag);
  }

  @UseGuards(JwtAuthGuard)
  @Get('insights/:id')
  findInsightById(@Param('id') id: string) {
    return this.aiService.findInsightById(id);
  }

  @UseGuards(JwtAuthGuard)
  @Patch('insights/:id')
  updateInsight(
    @Param('id') id: string,
    @Body() updateInsightDto: any,
  ) {
    return this.aiService.updateInsight(id, updateInsightDto);
  }

  @UseGuards(JwtAuthGuard)
  @Delete('insights/:id')
  removeInsight(@Param('id') id: string) {
    return this.aiService.removeInsight(id);
  }

  @UseGuards(JwtAuthGuard)
  @Post('insights/analyze')
  analyzeInsight(@Body() createInsightDto: CreateInsightDto) {
    return this.aiService.analyzeInsight(createInsightDto);
  }

  // AI Prompt endpoints
  @UseGuards(JwtAuthGuard)
  @Post('prompts')
  createAiPrompt(@Body() createAiPromptDto: CreateAiPromptDto) {
    return this.aiService.createAiPrompt(createAiPromptDto);
  }

  @UseGuards(JwtAuthGuard)
  @Get('prompts')
  findAllAiPrompts(
    @Query('companyId') companyId?: string,
    @Query('isSystem') isSystem?: boolean,
  ) {
    return this.aiService.findAllAiPrompts(companyId, isSystem);
  }

  @UseGuards(JwtAuthGuard)
  @Get('prompts/:id')
  findAiPromptById(@Param('id') id: string) {
    return this.aiService.findAiPromptById(id);
  }

  @UseGuards(JwtAuthGuard)
  @Patch('prompts/:id')
  updateAiPrompt(
    @Param('id') id: string,
    @Body() updateAiPromptDto: any,
  ) {
    return this.aiService.updateAiPrompt(id, updateAiPromptDto);
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'coAdmin')
  @Delete('prompts/:id')
  removeAiPrompt(@Param('id') id: string) {
    return this.aiService.removeAiPrompt(id);
  }

  // AI Chat endpoints
  @UseGuards(JwtAuthGuard)
  @Post('chats')
  createAiChat(@Body() createAiChatDto: CreateAiChatDto) {
    return this.aiService.createAiChat(createAiChatDto);
  }

  @UseGuards(JwtAuthGuard)
  @Get('chats')
  findAllAiChats(@Query('userId') userId: string) {
    return this.aiService.findAllAiChats(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('chats/:id')
  findAiChatById(@Param('id') id: string) {
    return this.aiService.findAiChatById(id);
  }

  @UseGuards(JwtAuthGuard)
  @Patch('chats/:id')
  updateAiChat(
    @Param('id') id: string,
    @Body() updateAiChatDto: any,
  ) {
    return this.aiService.updateAiChat(id, updateAiChatDto);
  }

  @UseGuards(JwtAuthGuard)
  @Delete('chats/:id')
  removeAiChat(@Param('id') id: string) {
    return this.aiService.removeAiChat(id);
  }

  @UseGuards(JwtAuthGuard)
  @Post('chats/:id/messages')
  sendChatMessage(
    @Param('id') id: string,
    @Body('message') message: string,
    @Body('userId') userId: string,
  ) {
    return this.aiService.sendChatMessage(id, message, userId);
  }

  // App Settings endpoints
  @UseGuards(JwtAuthGuard)
  @Post('settings')
  createAppSettings(@Body() createAppSettingsDto: CreateAppSettingsDto) {
    return this.aiService.createAppSettings(createAppSettingsDto);
  }

  @UseGuards(JwtAuthGuard)
  @Get('settings')
  findAllAppSettings(
    @Query('type') type?: string,
    @Query('companyId') companyId?: string,
  ) {
    return this.aiService.findAllAppSettings(type, companyId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('settings/:id')
  findAppSettingsById(@Param('id') id: string) {
    return this.aiService.findAppSettingsById(id);
  }

  @UseGuards(JwtAuthGuard)
  @Patch('settings/:id')
  updateAppSettings(
    @Param('id') id: string,
    @Body() updateAppSettingsDto: any,
  ) {
    return this.aiService.updateAppSettings(id, updateAppSettingsDto);
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'coAdmin')
  @Delete('settings/:id')
  removeAppSettings(@Param('id') id: string) {
    return this.aiService.removeAppSettings(id);
  }

  // Transcription endpoint
  @UseGuards(JwtAuthGuard)
  @Post('transcribe')
  transcribeAudio(@Body() transcribeAudioDto: TranscribeAudioDto) {
    return this.aiService.transcribeAudio(transcribeAudioDto);
  }
}
