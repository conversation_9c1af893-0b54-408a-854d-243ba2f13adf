# Server Configuration
PORT=8080
HOST=0.0.0.0
NODE_ENV=production
API_URL=https://api.yourdomain.com/api
FRONTEND_URL=https://yourdomain.com
NATIVE_URL=exp://yourdomain.com
API_PREFIX=api

# MongoDB Configuration
MONGODB_URI=mongodb+srv://username:<EMAIL>/database?retryWrites=true&w=majority

# JWT Configuration
JWT_SECRET=your_jwt_secret_key
JWT_REFRESH_SECRET=your_jwt_refresh_secret_key

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=your_aws_region
AWS_BUCKET=your_aws_bucket_name
AWS_PRIVATE_BUCKET=your_aws_private_bucket_name

# Email Configuration
MAIL_FROM_NAME=Your App Name
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_HOST=smtp.example.com
MAIL_PORT=587
MAIL_USERNAME=your_username
MAIL_PASSWORD=your_password
MAIL_SECURE=false
MAIL_PROVIDER=smtp # Options: smtp, ses, sendgrid, mailgun
