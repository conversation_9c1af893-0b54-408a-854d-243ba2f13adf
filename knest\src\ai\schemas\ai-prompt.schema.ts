import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type AiPromptDocument = AiPrompt & Document;

@Schema({
  collection: 'kd_ai_prompts',
  timestamps: true,
})
export class AiPrompt {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  template: string;

  @Prop({ required: false })
  description: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Company', required: false, index: true })
  companyId: MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true })
  userId: MongooseSchema.Types.ObjectId;

  @Prop({ type: Boolean, default: false })
  isSystem: boolean;
}

export const AiPromptSchema = SchemaFactory.createForClass(AiPrompt);
