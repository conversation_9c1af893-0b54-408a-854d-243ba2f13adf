import { <PERSON>p, <PERSON>hem<PERSON>, <PERSON><PERSON>aFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type AppSettingsDocument = AppSettings & Document;

@Schema({
  collection: 'kd_app_settings',
  timestamps: true,
  // When type is "AI_CONFIG", the settings array is expected to contain specific keys:
  // - provider: (string) e.g., "openai", "azure_openai", "anthropic"
  // - apiKey: (string) The API key for the selected provider.
  // - model: (string) The specific model to use, e.g., "gpt-4", "claude-2".
  // - apiBaseUrl: (string, optional) For self-hosted or proxy solutions, e.g., "https://myproxy.com/v1".
  // Example for "AI_CONFIG":
  // settings: [
  //   { key: "provider", value: "openai", type: "string" },
  //   { key: "apiKey", value: "your_api_key_here", type: "string" },
  //   { key: "model", value: "gpt-4", type: "string" },
  //   { key: "apiBaseUrl", value: "https://api.openai.com/v1", type: "string" } // Optional
  // ]
})
export class AppSettings {
  @Prop({ required: true })
  type: string; // e.g., 'INTEGRATION', 'FEATURE_FLAG', 'AI_CONFIG'

  @Prop({ required: true })
  name: string; // e.g., 'Slack Integration', 'New Dashboard', 'OpenAI Settings for Company X' or 'Global OpenAI Settings'

  @Prop({ type: [Object], default: [] })
  settings: Array<{
    key: string;
    value: any;
    type: string;
  }>;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true })
  userId: MongooseSchema.Types.ObjectId;
}

export const AppSettingsSchema = SchemaFactory.createForClass(AppSettings);
