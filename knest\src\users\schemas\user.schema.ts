import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { Profile } from '../../profiles/schemas/profile.schema';

export type UserDocument = User & Document;

@Schema({
  collection: 'kd_users',
  timestamps: true,
})
export class User {
  @Prop({ required: true, unique: true })
  email: string;

  @Prop({ required: true })
  password: string;

  @Prop({ type: [String], default: ['user'] })
  roles: string[];

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Profile' })
  profile: Profile;

  @Prop({ type: Date, default: null })
  lastLogin: Date;

  @Prop({ type: Object, default: {} })
  settings: Record<string, any>;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Company', required: false })
  companyId: MongooseSchema.Types.ObjectId;

}

export const UserSchema = SchemaFactory.createForClass(User);
