import { Injectable, NotFoundException, OnModuleInit } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ObjectId } from 'mongoose';
import { Company, CompanyDocument } from './schemas/company.schema';
import { CreateCompanyDto } from './dto/create-company.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';
import { CreateTeamDto } from '../teams/dto/create-team.dto';
import * as crypto from 'crypto';
import { ModuleRef } from '@nestjs/core';

@Injectable()
export class CompaniesService implements OnModuleInit {
  private postsService: any;
  private teamsService: any;

  constructor(
    @InjectModel(Company.name) private companyModel: Model<CompanyDocument>,
    private moduleRef: ModuleRef
  ) {}

  onModuleInit() {
    // We use moduleRef to avoid circular dependency
    try {
      // Import the PostsService class dynamically to avoid circular dependency
      const { PostsService } = require('../posts/posts.service');
      this.postsService = this.moduleRef.get(PostsService, { strict: false });
    } catch (error) {
      console.warn('Could not load PostsService:', error.message);
    }

    try {
      // Import the TeamsService class dynamically to avoid circular dependency
      const { TeamsService } = require('../teams/teams.service');
      this.teamsService = this.moduleRef.get(TeamsService, { strict: false });
    } catch (error) {
      console.warn('Could not load TeamsService:', error.message);
    }
  }

  async create(createCompanyDto: CreateCompanyDto): Promise<CompanyDocument> {
    // Generate invite code if not provided
    if (!createCompanyDto.invite_code) {
      createCompanyDto.invite_code = this.generateInviteCode();
    }

    const newCompany = new this.companyModel(createCompanyDto);
    const company = await newCompany.save();

    // Create default post types if postsService is available
    if (this.postsService) {
      await this.createDefaultPostTypes((company._id as ObjectId).toString(), createCompanyDto.createdBy);
    }

    // Create default teams if teamsService is available
    if (this.teamsService) {
      await this.createDefaultTeams((company._id as ObjectId).toString(), createCompanyDto.createdBy);
    }

    return company;
  }

  async createDefaultTeams(companyId: string, userId: string): Promise<void> {
    if (!this.teamsService) {
      console.warn('TeamsService not available, skipping creation of default teams.');
      return;
    }

    const templateCompanyId = "682f32c984a2f20bec505d5e";

    try {
      const templateTeams = await this.teamsService.findByCompany(templateCompanyId);

      if (!templateTeams || templateTeams.length === 0) {
        console.warn(`No teams found for template company ${templateCompanyId}. Skipping default team creation.`);
        return;
      }

      for (const templateTeam of templateTeams) {
        const newTeamDto: CreateTeamDto = {
          name: templateTeam.name,
          description: templateTeam.description,
          highlightColor: templateTeam.highlightColor,
          companyId: companyId,
          userId: userId, // creator of the team
        };
        await this.teamsService.create(newTeamDto, userId); // Pass userId as second argument for creator
      }
    } catch (error) {
      console.error('Error creating default teams:', error.message);
    }
  }

  async createDefaultPostTypes(companyId: string, userId: string): Promise<void> {
    if (!this.postsService) {
      console.warn('PostsService not available, skipping creation of default post types.');
      return;
    }

    const templateCompanyId = "682f32c984a2f20bec505d5e";

    try {
      const templatePostTypes = await this.postsService.findAllPostTypes(templateCompanyId);

      if (!templatePostTypes || templatePostTypes.length === 0) {
        console.warn(`No post types found for template company ${templateCompanyId}. Skipping default post type creation.`);
        return;
      }

      for (const templatePostType of templatePostTypes) {
        if (templatePostType.archived) {
          continue; // Skip archived post types
        }

        const newPostType = {
          name: templatePostType.name,
          description: templatePostType.description,
          recordTips: templatePostType.recordTips,
          highlightColor: templatePostType.highlightColor,
          companyId: companyId,
          userId: userId,
          archived: false,
        };

        await this.postsService.createPostType(newPostType);
      }
    } catch (error) {
      console.error('Error creating default post types:', error.message);
      // Optionally, re-throw the error or handle it as per application's error handling strategy
    }
  }

  async findAll(): Promise<CompanyDocument[]> {
    return this.companyModel.find().exec();
  }

  async findById(id: string): Promise<CompanyDocument> {
    const company = await this.companyModel.findById(id).exec();
    if (!company) {
      throw new NotFoundException(`Company with ID ${id} not found`);
    }
    return company;
  }

  async findByInviteCode(inviteCode: string): Promise<CompanyDocument | null> {
    return this.companyModel.findOne({ invite_code: inviteCode }).exec();
  }

  async update(id: string, updateCompanyDto: UpdateCompanyDto): Promise<CompanyDocument> {
    const updatedCompany = await this.companyModel
      .findByIdAndUpdate(id, updateCompanyDto, { new: true })
      .exec();

    if (!updatedCompany) {
      throw new NotFoundException(`Company with ID ${id} not found`);
    }

    return updatedCompany;
  }

  async remove(id: string): Promise<CompanyDocument> {
    const deletedCompany = await this.companyModel.findByIdAndDelete(id).exec();

    if (!deletedCompany) {
      throw new NotFoundException(`Company with ID ${id} not found`);
    }

    return deletedCompany;
  }

  private generateInviteCode(): string {
    // Generate a random 8-character alphanumeric string
    return crypto.randomBytes(4).toString('hex');
  }
}

