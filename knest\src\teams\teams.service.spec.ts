import { Test, TestingModule } from '@nestjs/testing';
import { TeamsService } from './teams.service';
import { getModelToken } from '@nestjs/mongoose';
import { Team } from './schemas/team.schema';
import { Membership } from './schemas/membership.schema';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { Model } from 'mongoose';

describe('TeamsService', () => {
  let service: TeamsService;
  let teamModel: Model<Team>;
  let membershipModel: Model<Membership>;

  // Static methods for Team
  const mockTeamModelStatics = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
    create: jest.fn(),
    new: jest.fn(),
  };
  function MockTeamModelConstructor(dto) {
    return {
      ...dto,
      save: jest.fn().mockResolvedValue({ _id: 'team1', ...dto }),
    };
  }
  Object.assign(MockTeamModelConstructor, mockTeamModelStatics);

  // Static methods for Membership
  const mockMembershipModelStatics = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
    create: jest.fn(),
    new: jest.fn(),
    deleteMany: jest.fn(),
    findOneAndDelete: jest.fn(),
    deleteOne: jest.fn(),
  };
  function MockMembershipModelConstructor(dto) {
    return {
      ...dto,
      save: jest.fn().mockResolvedValue({ _id: 'membership1', ...dto }),
    };
  }
  Object.assign(MockMembershipModelConstructor, mockMembershipModelStatics);

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TeamsService,
        {
          provide: getModelToken(Team.name),
          useValue: MockTeamModelConstructor as any,
        },
        {
          provide: getModelToken(Membership.name),
          useValue: MockMembershipModelConstructor as any,
        },
      ],
    }).compile();

    service = module.get<TeamsService>(TeamsService);
    teamModel = module.get<Model<Team>>(getModelToken(Team.name));
    membershipModel = module.get<Model<Membership>>(getModelToken(Membership.name));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new team and add creator as member', async () => {
      const createTeamDto = {
        name: 'Test Team',
        companyId: 'company1',
        userId: 'user1',
        description: 'Test team description',
      };

      const savedTeam = {
        _id: 'team1',
        ...createTeamDto,
      };

      const savedMembership = {
        _id: 'membership1',
        teamId: 'team1',
        memberId: 'user1',
      };

      // Mock the team model constructor and save
      (teamModel as any).new = jest.fn().mockImplementation(() => ({
        save: jest.fn().mockResolvedValue(savedTeam),
      }));

      // Mock the addMember method
      jest.spyOn(service, 'addMember').mockResolvedValue(savedMembership as any);

      const result = await service.create(createTeamDto);

      expect(result).toEqual(savedTeam);
      expect(service.addMember).toHaveBeenCalledWith({
        teamId: 'team1',
        memberId: 'user1',
      });
    });
  });

  describe('findAll', () => {
    it('should return all teams', async () => {
      const mockTeams = [
        { _id: 'team1', name: 'Team 1' },
        { _id: 'team2', name: 'Team 2' },
      ];

      (teamModel as any).find.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockTeams),
      });

      const result = await service.findAll();

      expect(teamModel.find).toHaveBeenCalled();
      expect(result).toEqual(mockTeams);
    });
  });

  describe('findByCompany', () => {
    it('should return teams for a specific company', async () => {
      const mockTeams = [
        { _id: 'team1', name: 'Team 1', companyId: 'company1' },
      ];

      (teamModel as any).find.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockTeams),
      });

      const result = await service.findByCompany('company1');

      expect(teamModel.find).toHaveBeenCalledWith({ companyId: 'company1' });
      expect(result).toEqual(mockTeams);
    });
  });

  describe('findById', () => {
    it('should return a team when it exists', async () => {
      const mockTeam = { _id: 'team1', name: 'Test Team' };

      (teamModel as any).findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockTeam),
      });

      const result = await service.findById('team1');

      expect(teamModel.findById).toHaveBeenCalledWith('team1');
      expect(result).toEqual(mockTeam);
    });

    it('should throw NotFoundException when team does not exist', async () => {
      (teamModel as any).findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.findById('nonexistent')).rejects.toThrow(NotFoundException);
      expect(teamModel.findById).toHaveBeenCalledWith('nonexistent');
    });
  });

  describe('update', () => {
    it('should update a team when it exists', async () => {
      const updateTeamDto = {
        name: 'Updated Team',
        description: 'Updated description',
      };

      const updatedTeam = {
        _id: 'team1',
        ...updateTeamDto,
      };

      (teamModel as any).findByIdAndUpdate.mockReturnValue({
        exec: jest.fn().mockResolvedValue(updatedTeam),
      });

      const result = await service.update('team1', updateTeamDto);

      expect(teamModel.findByIdAndUpdate).toHaveBeenCalledWith('team1', updateTeamDto, { new: true });
      expect(result).toEqual(updatedTeam);
    });

    it('should throw NotFoundException when team does not exist', async () => {
      (teamModel as any).findByIdAndUpdate.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.update('nonexistent', { name: 'Updated' })).rejects.toThrow(NotFoundException);
      expect(teamModel.findByIdAndUpdate).toHaveBeenCalledWith('nonexistent', { name: 'Updated' }, { new: true });
    });
  });

  describe('remove', () => {
    it('should remove a team and its memberships', async () => {
      const deletedTeam = { _id: 'team1', name: 'Test Team' };

      (membershipModel as any).deleteMany.mockReturnValue({
        exec: jest.fn().mockResolvedValue({ deletedCount: 2 }),
      });

      (teamModel as any).findByIdAndDelete.mockReturnValue({
        exec: jest.fn().mockResolvedValue(deletedTeam),
      });

      const result = await service.remove('team1');

      expect(membershipModel.deleteMany).toHaveBeenCalledWith({ teamId: 'team1' });
      expect(teamModel.findByIdAndDelete).toHaveBeenCalledWith('team1');
      expect(result).toEqual(deletedTeam);
    });

    it('should throw NotFoundException when team does not exist', async () => {
      (membershipModel as any).deleteMany.mockReturnValue({
        exec: jest.fn().mockResolvedValue({ deletedCount: 0 }),
      });

      (teamModel as any).findByIdAndDelete.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.remove('nonexistent')).rejects.toThrow(NotFoundException);
      expect(teamModel.findByIdAndDelete).toHaveBeenCalledWith('nonexistent');
    });
  });

  describe('getTeamMembers', () => {
    it('should return members of a team', async () => {
      const mockMemberships = [
        { _id: 'membership1', teamId: 'team1', memberId: { _id: 'user1', email: '<EMAIL>' } },
        { _id: 'membership2', teamId: 'team1', memberId: { _id: 'user2', email: '<EMAIL>' } },
      ];

      (membershipModel as any).find.mockReturnValue({
        populate: jest.fn().mockReturnValue({
          exec: jest.fn().mockResolvedValue(mockMemberships),
        }),
      });

      const result = await service.getTeamMembers('team1');

      expect(membershipModel.find).toHaveBeenCalledWith({ teamId: 'team1' });
      expect(result).toEqual(mockMemberships);
    });
  });

  describe('getUserTeams', () => {
    it('should return teams that a user is a member of', async () => {
      const mockMemberships = [
        { teamId: 'team1' },
        { teamId: 'team2' },
      ];

      const mockTeams = [
        { _id: 'team1', name: 'Team 1' },
        { _id: 'team2', name: 'Team 2' },
      ];

      (membershipModel as any).find.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockMemberships),
      });

      (teamModel as any).find.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockTeams),
      });

      const result = await service.getUserTeams('user1');

      expect(membershipModel.find).toHaveBeenCalledWith({ memberId: 'user1' });
      expect(teamModel.find).toHaveBeenCalledWith({ _id: { $in: ['team1', 'team2'] } });
      expect(result).toEqual(mockTeams);
    });
  });

  describe('addMember', () => {
    it('should add a member to a team', async () => {
      const createMembershipDto = {
        teamId: 'team1',
        memberId: 'user1',
      };

      const mockTeam = { _id: 'team1', name: 'Test Team' };
      const savedMembership = { _id: 'membership1', ...createMembershipDto };

      (teamModel as any).findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockTeam),
      });

      (membershipModel as any).findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      (membershipModel as any).new = jest.fn().mockImplementation(() => ({
        save: jest.fn().mockResolvedValue(savedMembership),
      }));

      const result = await service.addMember(createMembershipDto);

      expect(teamModel.findById).toHaveBeenCalledWith('team1');
      expect(membershipModel.findOne).toHaveBeenCalledWith({
        teamId: 'team1',
        memberId: 'user1',
      });
      expect(result).toEqual(savedMembership);
    });

    it('should throw NotFoundException when team does not exist', async () => {
      (teamModel as any).findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.addMember({ teamId: 'nonexistent', memberId: 'user1' })).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException when membership already exists', async () => {
      const mockTeam = { _id: 'team1', name: 'Test Team' };
      const existingMembership = { _id: 'membership1', teamId: 'team1', memberId: 'user1' };

      (teamModel as any).findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockTeam),
      });

      (membershipModel as any).findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(existingMembership),
      });

      await expect(service.addMember({ teamId: 'team1', memberId: 'user1' })).rejects.toThrow(BadRequestException);
    });
  });

  describe('removeMember', () => {
    it('should remove a member from a team', async () => {
      (membershipModel as any).deleteOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue({ deletedCount: 1 }),
      });
      const result = await service.removeMember('team1', 'user1');
      expect((membershipModel as any).deleteOne).toHaveBeenCalledWith({
        teamId: 'team1',
        memberId: 'user1',
      });
      expect(result).toBeUndefined();
    });

    it('should throw NotFoundException when membership does not exist', async () => {
      (membershipModel as any).deleteOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue({ deletedCount: 0 }),
      });
      await expect(service.removeMember('team1', 'nonexistent')).rejects.toThrow(NotFoundException);
    });
  });
});
