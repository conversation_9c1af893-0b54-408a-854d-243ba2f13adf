import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { BaseSchema } from './base.schema';

@Schema()
export class User extends BaseSchema {
  @Prop({ required: true })
  email: string;

  @Prop()
  password: string;

  @Prop()
  profile: {
    firstName: string;
    lastName: string;
    avatar: string;
  };

  @Prop({ default: [] })
  roles: string[];
}

export const UserSchema = SchemaFactory.createForClass(User);