import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../../users/users.service';

@Injectable()
export class UserContextMiddleware implements NestMiddleware {
  constructor(
    private readonly jwtService: JwtService,
    private readonly usersService: UsersService,
  ) {}

  async use(req: Request, res: Response, next: NextFunction) {
    try {
      const authHeader = req.headers.authorization;
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        const payload = this.jwtService.verify(token);
        
        if (payload && payload.sub) {
          // Get the user's profile with company information
          const user = await this.usersService.findById(payload.sub);
          if (user && user.profile && user.companyId) {
            // Add user context to the request object
            req['userContext'] = {
              userId: payload.sub,
              companyId: user.companyId.toString(),
              roles: payload.roles || [],
            };
          }
        }
      }
    } catch (error) {
      // If token verification fails, continue without user context
      console.error('Error extracting user context:', error.message);
    }
    
    next();
  }
}

// Extend the Express Request interface to include userContext
declare global {
  namespace Express {
    interface Request {
      userContext?: {
        userId: string;
        companyId: string;
        roles: string[];
      };
    }
  }
}
