// Configuration for performance tests
export const config = {
  // API base URL - use environment variable or default to localhost:3011
  apiBaseUrl: __ENV.API_BASE_URL || 'http://localhost:3011/api',
  
  // Test user credentials
  testUser: {
    email: __ENV.TEST_USER_EMAIL || '<EMAIL>',
    password: __ENV.TEST_USER_PASSWORD || 'Password123!',
  },
  
  // Performance thresholds
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests should be below 500ms
    login_success: ['count>100'],     // We should have at least 100 successful logins
    post_create_success: ['count>50'], // We should have at least 50 successful post creations
    post_read_success: ['count>100'],  // We should have at least 100 successful post reads
  },
  
  // Load test stages
  stages: {
    auth: [
      { duration: '30s', target: 20 }, // Ramp up to 20 users over 30 seconds
      { duration: '1m', target: 20 },  // Stay at 20 users for 1 minute
      { duration: '30s', target: 0 },  // Ramp down to 0 users over 30 seconds
    ],
    posts: [
      { duration: '20s', target: 10 }, // Ramp up to 10 users over 20 seconds
      { duration: '1m', target: 10 },  // Stay at 10 users for 1 minute
      { duration: '20s', target: 0 },  // Ramp down to 0 users over 20 seconds
    ],
  },
};
