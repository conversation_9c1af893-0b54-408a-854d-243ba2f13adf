import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type FileUploadDocument = FileUpload & Document;

@Schema({
  collection: 'kd_file_uploads',
  timestamps: true,
})
export class FileUpload {
  @Prop({ required: true })
  originalName: string;

  @Prop({ required: true })
  mimeType: string;

  @Prop({ required: true })
  size: number;

  @Prop({ required: true })
  key: string;

  @Prop({ required: true })
  url: string;

  @Prop({ 
    type: String, 
    required: true,
    enum: ['avatar', 'logo', 'audio', 'document', 'other'] 
  })
  type: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true })
  userId: MongooseSchema.Types.ObjectId;

  @Prop({ 
    type: MongooseSchema.Types.ObjectId, 
    refPath: 'objectType', 
    required: false 
  })
  linkedObjectId: MongooseSchema.Types.ObjectId;

  @Prop({ 
    type: String, 
    required: false,
    enum: ['Post', 'User', 'Company', 'Insight'] 
  })
  objectType: string;
}

export const FileUploadSchema = SchemaFactory.createForClass(FileUpload);
