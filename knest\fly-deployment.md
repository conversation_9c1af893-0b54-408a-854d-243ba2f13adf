# Deploying Kodi Nest to Fly.io

This guide explains how to deploy the Kodi Nest backend to Fly.io.

## Prerequisites

1. Install the Fly CLI: https://fly.io/docs/hands-on/install-flyctl/
2. Sign up for a Fly.io account: https://fly.io/docs/hands-on/sign-up/
3. Log in to Fly.io: `fly auth login`

## Deployment Steps

### 1. Set up Environment Variables

Create a `.env` file with your production environment variables. You can use the `.env.example` file as a template.

To set environment variables on Fly.io, use the following command:

```bash
fly secrets set MONGODB_URI="your-mongodb-connection-string" \
  JWT_SECRET="your-jwt-secret" \
  JWT_REFRESH_SECRET="your-jwt-refresh-secret" \
  AWS_ACCESS_KEY_ID="your-aws-access-key" \
  AWS_SECRET_ACCESS_KEY="your-aws-secret-key" \
  AWS_REGION="your-aws-region" \
  AWS_BUCKET="your-aws-bucket" \
  AWS_PRIVATE_BUCKET="your-aws-private-bucket" \
  HOST="0.0.0.0" \
  PORT="8080" \
  API_PREFIX="api" \
  API_URL="https://your-api-url.fly.dev/api" \
  FRONTEND_URL="https://your-frontend-url.com" \
  NATIVE_URL="exp://your-native-url.com" \
  MAIL_PROVIDER="smtp" \
  MAIL_HOST="your-smtp-host" \
  MAIL_PORT="587" \
  MAIL_SECURE="false" \
  MAIL_USERNAME="your-smtp-username" \
  MAIL_PASSWORD="your-smtp-password" \
  MAIL_FROM_NAME="Your App Name" \
  MAIL_FROM_ADDRESS="<EMAIL>"
```

If you're using AWS SES for email instead of SMTP, use this configuration:

```bash
fly secrets set MONGODB_URI="your-mongodb-connection-string" \
  JWT_SECRET="your-jwt-secret" \
  JWT_REFRESH_SECRET="your-jwt-refresh-secret" \
  AWS_ACCESS_KEY_ID="your-aws-access-key" \
  AWS_SECRET_ACCESS_KEY="your-aws-secret-key" \
  AWS_REGION="your-aws-region" \
  AWS_BUCKET="your-aws-bucket" \
  AWS_PRIVATE_BUCKET="your-aws-private-bucket" \
  FRONTEND_URL="https://your-frontend-url.com" \
  MAIL_PROVIDER="ses" \
  MAIL_FROM_NAME="Your App Name" \
  MAIL_FROM_ADDRESS="<EMAIL>"
```

### 2. Create a Volume for Temporary Audio Uploads

While most file uploads in the application go directly to AWS S3, audio uploads are temporarily stored in a local volume before processing. Create a volume for these temporary audio files:

```bash
fly volumes create kodi_nest_data --size 1 --region iad
```

> **Note**: This volume is only used for temporary storage of audio files during processing. All permanent file storage uses AWS S3, which is configured through the AWS environment variables.

### 3. Update Dependencies and Deploy the Application

Before deploying, make sure your package-lock.json is up to date with all dependencies:

```bash
# Run the update-dependencies script
node update-dependencies.js
```

Then deploy the application to Fly.io:

```bash
fly deploy
```

For convenience, you can use the provided deployment scripts:

**On Linux/macOS:**
```bash
# Make the script executable
chmod +x deploy.sh

# Run the script
./deploy.sh
```

**On Windows:**
```bash
deploy.bat
```

If you encounter any issues with dependencies during deployment, you can also try:

```bash
# Force a clean install of dependencies
npm ci --force

# Then deploy
fly deploy
```

### 4. Monitor the Deployment

Monitor the deployment logs:

```bash
fly logs
```

### 5. Open the Application

Open the deployed application:

```bash
fly open
```

## Scaling

To scale your application, you can adjust the settings in the `fly.toml` file:

- Change `min_machines_running` to control the minimum number of instances
- Adjust `memory_mb` and `cpus` to change the resources allocated to each instance

After making changes, redeploy with:

```bash
fly deploy
```

## Troubleshooting

If you encounter issues, check the logs:

```bash
fly logs
```

For more detailed information, use:

```bash
fly status
```

## File Storage Configuration

The application uses AWS S3 for permanent file storage. Make sure your AWS S3 buckets are properly configured:

1. **Public Bucket**: Used for general file uploads that need to be publicly accessible
   - Set up proper CORS configuration to allow uploads from your frontend domains
   - Configure public read access for objects

2. **Private Bucket**: Used for sensitive files like user avatars and company logos
   - Set up proper CORS configuration to allow uploads from your frontend domains
   - Keep objects private and use presigned URLs for access

Example S3 CORS configuration:

```json
[
  {
    "AllowedHeaders": ["*"],
    "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
    "AllowedOrigins": [
      "https://your-frontend-url.com",
      "https://your-api-url.fly.dev"
    ],
    "ExposeHeaders": ["ETag"]
  }
]
```

## Additional Resources

- [Fly.io Documentation](https://fly.io/docs/)
- [NestJS Deployment Guide](https://docs.nestjs.com/techniques/performance)
- [MongoDB Atlas Connection](https://www.mongodb.com/docs/atlas/connect-to-database-deployment/)
- [AWS S3 Documentation](https://docs.aws.amazon.com/s3/)
