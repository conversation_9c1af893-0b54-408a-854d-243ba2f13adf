import { Test, TestingModule } from '@nestjs/testing';
import { PostsService } from './posts.service';
import { getModelToken } from '@nestjs/mongoose';
import { Post } from './schemas/post.schema';
import { PostType } from './schemas/post-type.schema';
import { Tag } from './schemas/tag.schema';
import { Comment } from './schemas/comment.schema';
import { ModuleRef } from '@nestjs/core';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { Model } from 'mongoose';

describe('PostsService', () => {
  let service: PostsService;
  let postModel: Model<Post>;
  let postTypeModel: Model<PostType>;
  let tagModel: Model<Tag>;
  let commentModel: Model<Comment>;

  // Mock constructor for postModel
  const mockPostModelStatics = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
    create: jest.fn(),
    new: jest.fn(),
  };
  function MockPostModelConstructor(dto) {
    return {
      ...dto,
      save: jest.fn().mockResolvedValue({ _id: '507f1f77bcf86cd799439011', ...dto }),
    };
  }
  Object.assign(MockPostModelConstructor, mockPostModelStatics);

  const mockPostTypeModel = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
    create: jest.fn(),
    new: jest.fn(),
  };

  const mockTagModel = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
    create: jest.fn(),
    new: jest.fn(),
  };

  const mockCommentModel = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
    create: jest.fn(),
    new: jest.fn(),
    deleteMany: jest.fn(),
  };

  const mockModuleRef = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PostsService,
        {
          provide: getModelToken(Post.name),
          useValue: MockPostModelConstructor as any,
        },
        {
          provide: getModelToken(PostType.name),
          useValue: mockPostTypeModel,
        },
        {
          provide: getModelToken(Tag.name),
          useValue: mockTagModel,
        },
        {
          provide: getModelToken(Comment.name),
          useValue: mockCommentModel,
        },
        {
          provide: ModuleRef,
          useValue: mockModuleRef,
        },
      ],
    }).compile();

    service = module.get<PostsService>(PostsService);
    postModel = module.get<Model<Post>>(getModelToken(Post.name));
    postTypeModel = module.get<Model<PostType>>(getModelToken(PostType.name));
    tagModel = module.get<Model<Tag>>(getModelToken(Tag.name));
    commentModel = module.get<Model<Comment>>(getModelToken(Comment.name));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAllPosts', () => {
    it('should return all posts with no filters', async () => {
      const mockPosts = [
        { _id: '507f1f77bcf86cd799439011', body: 'Test post 1' },
        { _id: '507f1f77bcf86cd799439012', body: 'Test post 2' },
      ];

      (postModel as any).find.mockReturnValue({
        sort: jest.fn().mockReturnValue({
          exec: jest.fn().mockResolvedValue(mockPosts),
        }),
      });

      const result = await service.findAllPosts();

      expect((postModel as any).find).toHaveBeenCalledWith({});
      expect(result).toEqual(mockPosts);
    });

    it('should apply filters when provided', async () => {
      const mockPosts = [{ _id: '507f1f77bcf86cd799439011', body: 'Test post 1' }];

      (postModel as any).find.mockReturnValue({
        sort: jest.fn().mockReturnValue({
          exec: jest.fn().mockResolvedValue(mockPosts),
        }),
      });

      const result = await service.findAllPosts('company1', 'team1', 'postType1', 'tag1', false);

      expect((postModel as any).find).toHaveBeenCalledWith({
        companyId: 'company1',
        teamId: 'team1',
        postTypeId: 'postType1',
        hashtags: 'tag1',
        isDraft: false,
      });
      expect(result).toEqual(mockPosts);
    });
  });

  describe('findPostById', () => {
    const validId = '507f1f77bcf86cd799439011';
    const notFoundId = '507f1f77bcf86cd799439012';
    it('should return a post when it exists', async () => {
      const mockPost = { _id: validId, body: 'Test post 1' };

      (postModel as any).findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockPost),
      });

      const result = await service.findPostById(validId);

      expect((postModel as any).findById).toHaveBeenCalledWith(validId);
      expect(result).toEqual(mockPost);
    });

    it('should throw NotFoundException when post does not exist', async () => {
      (postModel as any).findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.findPostById(notFoundId)).rejects.toThrow(NotFoundException);
      expect((postModel as any).findById).toHaveBeenCalledWith(notFoundId);
    });
  });

  describe('createPost', () => {
    it('should create a new post and extract hashtags', async () => {
      const createPostDto = {
        body: 'Test post with #hashtag1 and #hashtag2',
        userId: 'user1',
        companyId: 'company1',
      };
      const savedPost = {
        _id: '507f1f77bcf86cd799439011',
        ...createPostDto,
        hashtags: ['hashtag1', 'hashtag2'],
      };
      // Spy on the constructor
      const postModelConstructorSpy = jest.spyOn(postModel.prototype, 'constructor');
      // Mock the save method on the instance
      const mockSave = jest.fn().mockResolvedValue(savedPost);
      // Mock the constructor to return an object with save
      (postModel as any).mockImplementation = (dto) => ({ ...dto, save: mockSave });
      // Patch the service to use the mockImplementation
      (service as any).postModel = function(dto) { return (postModel as any).mockImplementation(dto); };
      (service as any).updateTags = jest.fn().mockResolvedValue(undefined);
      const result = await service.createPost(createPostDto);
      expect(result).toEqual(savedPost);
      expect((service as any).updateTags).toHaveBeenCalledWith(['hashtag1', 'hashtag2'], 'company1');
      // Optionally, check that the constructor was called with the right args
      // (not strictly necessary if the save result is correct)
    });
  });

  describe('updatePost', () => {
    it('should update a post and extract hashtags if body is updated', async () => {
      const updatePostDto = {
        body: 'Updated post with #newtag',
      };
      const postId = '507f1f77bcf86cd799439011';
      const existingPost = {
        _id: postId,
        companyId: 'company1',
      };
      const updatedPost = {
        _id: postId,
        body: updatePostDto.body,
        hashtags: ['newtag'],
      };
      (postModel as any).findById = jest.fn().mockReturnValue({
        exec: jest.fn().mockResolvedValue(existingPost),
      });
      (postModel as any).findByIdAndUpdate = jest.fn().mockReturnValue({
        exec: jest.fn().mockResolvedValue(updatedPost),
      });
      (service as any).updateTags = jest.fn().mockResolvedValue(undefined);
      const result = await service.updatePost(postId, updatePostDto);
      expect((postModel as any).findById).toHaveBeenCalledWith(postId);
      expect((service as any).updateTags).toHaveBeenCalledWith(['newtag'], 'company1');
      expect(result).toEqual(updatedPost);
    });
    it('should throw NotFoundException when post does not exist', async () => {
      const updatePostDto = { body: 'Updated' };
      const postId = '507f1f77bcf86cd799439012';
      (postModel as any).findByIdAndUpdate = jest.fn().mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });
      await expect(service.updatePost(postId, updatePostDto)).rejects.toThrow(NotFoundException);
    });
  });

  describe('removePost', () => {
    const validId = '507f1f77bcf86cd799439011';
    const notFoundId = '507f1f77bcf86cd799439012';
    it('should remove a post and its comments', async () => {
      const deletedPost = { _id: validId, body: 'Test post' };

      (commentModel as any).deleteMany.mockReturnValue({
        exec: jest.fn().mockResolvedValue({ deletedCount: 2 }),
      });

      (postModel as any).findByIdAndDelete.mockReturnValue({
        exec: jest.fn().mockResolvedValue(deletedPost),
      });

      const result = await service.removePost(validId);

      expect((commentModel as any).deleteMany).toHaveBeenCalledWith({
        linkedObjectId: validId,
        objectType: 'Post',
      });
      expect((postModel as any).findByIdAndDelete).toHaveBeenCalledWith(validId);
      expect(result).toEqual(deletedPost);
    });
    it('should throw NotFoundException when post does not exist', async () => {
      (commentModel as any).deleteMany.mockReturnValue({
        exec: jest.fn().mockResolvedValue({ deletedCount: 0 }),
      });

      (postModel as any).findByIdAndDelete.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.removePost(notFoundId)).rejects.toThrow(NotFoundException);
    });
  });
});
