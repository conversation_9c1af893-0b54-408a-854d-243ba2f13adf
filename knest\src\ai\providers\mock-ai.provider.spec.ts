import { MockAiProvider } from './mock-ai.provider';
import { ChatMessage } from '../schemas/ai-chat.schema';

describe('MockAiProvider', () => {
  let provider: MockAiProvider;

  beforeEach(() => {
    provider = new MockAiProvider();
  });

  describe('generateText', () => {
    it('should return a mock string including the prompt', async () => {
      const prompt = 'Test prompt';
      const result = await provider.generateText(prompt);
      expect(result).toContain(prompt);
      expect(result).toContain('Mocked AI response for prompt:');
      expect(result).toContain('default_mock_model');
    });

    it('should include the model if provided in options', async () => {
      const prompt = 'Test prompt with model';
      const model = 'custom_mock_model';
      const result = await provider.generateText(prompt, { model });
      expect(result).toContain(model);
    });
  });

  describe('createChatCompletion', () => {
    it('should return a mock ChatMessage including the last message content', async () => {
      const messages: ChatMessage[] = [
        { role: 'user', content: 'Hello', timestamp: new Date() },
        { role: 'assistant', content: 'Hi there!', timestamp: new Date() },
        { role: 'user', content: 'How are you?', timestamp: new Date() },
      ];
      const result = await provider.createChatCompletion(messages);
      expect(result).toBeDefined();
      expect(result?.role).toBe('assistant');
      expect(result?.content).toContain('How are you?');
      expect(result?.content).toContain('Mocked AI chat response to:');
      expect(result?.content).toContain('default_mock_model');
      expect(result?.content).toContain('0.7'); // Default temperature
      expect(result?.timestamp).toBeInstanceOf(Date);
    });

    it('should handle empty messages array', async () => {
      const messages: ChatMessage[] = [];
      const result = await provider.createChatCompletion(messages);
      expect(result?.content).toContain('no message');
    });
    
    it('should include model and temperature if provided in options', async () => {
      const messages: ChatMessage[] = [{ role: 'user', content: 'Test message', timestamp: new Date() }];
      const model = 'custom_chat_model';
      const temperature = 0.9;
      const result = await provider.createChatCompletion(messages, { model, temperature });
      expect(result?.content).toContain(model);
      expect(result?.content).toContain(temperature.toString());
    });
  });

  describe('transcribe', () => {
    it('should return a mock transcription string including the audio URL', async () => {
      const audioUrl = 'http://example.com/audio.mp3';
      const result = await provider.transcribe(audioUrl);
      expect(result).toContain(audioUrl);
      expect(result).toContain('Mocked transcription for audio:');
      expect(result).toContain('default_mock_whisper');
      expect(result).toContain('en'); // Default language
    });

    it('should include model and language if provided in options', async () => {
      const audioUrl = 'http://example.com/audio.mp3';
      const model = 'custom_whisper_model';
      const language = 'fr';
      const result = await provider.transcribe(audioUrl, { model, language });
      expect(result).toContain(model);
      expect(result).toContain(language);
    });
  });
});
