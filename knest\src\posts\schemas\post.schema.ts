import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type PostDocument = Post & Document;

@Schema({
  collection: 'kd_posts',
  timestamps: true,
})
export class Post {
  @Prop({ required: true })
  body: string;

  @Prop({ required: false })
  enhancedBody: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Company', required: false, index: true })
  companyId: MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Team', required: false, index: true })
  teamId: MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'PostType', required: false, index: true })
  postTypeId: MongooseSchema.Types.ObjectId;

  @Prop({ type: [String], default: [] })
  hashtags: string[];

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true, index: true })
  userId: MongooseSchema.Types.ObjectId;

  @Prop({ type: Boolean, default: false })
  isDraft: boolean;

  @Prop({ type: [{ type: MongooseSchema.Types.ObjectId, ref: 'Comment' }] })
  comments: MongooseSchema.Types.ObjectId[];

  @Prop({ type: String, required: false })
  audioUrl: string;
}

export const PostSchema = SchemaFactory.createForClass(Post);
