/**
 * This script updates the package-lock.json file with the new dependencies
 * Run this script before deploying to fly.io
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('Updating dependencies...');

try {
  // Check if package.json exists
  if (!fs.existsSync(path.join(__dirname, 'package.json'))) {
    console.error('package.json not found');
    process.exit(1);
  }

  // Install dependencies to update package-lock.json
  console.log('Running npm install to update package-lock.json...');
  execSync('npm install', { stdio: 'inherit' });

  console.log('Dependencies updated successfully!');
  console.log('You can now deploy to fly.io with:');
  console.log('fly deploy');
} catch (error) {
  console.error('Error updating dependencies:', error.message);
  process.exit(1);
}
