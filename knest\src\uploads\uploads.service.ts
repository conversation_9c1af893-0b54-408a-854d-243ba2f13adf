import { Injectable } from '@nestjs/common';
import { S3 } from 'aws-sdk';
import { v4 as uuid } from 'uuid';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class UploadsService {
  private s3: S3;

  constructor() {
    this.s3 = new S3({
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      region: process.env.AWS_REGION || process.env.AWS_DEFAULT_REGION,
    });

    // Create uploads directory if it doesn't exist
    const uploadsDir = path.join(process.cwd(), 'uploads');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir);
    }

    // Create audio uploads directory if it doesn't exist
    const audioUploadsDir = path.join(uploadsDir, 'audio');
    if (!fs.existsSync(audioUploadsDir)) {
      fs.mkdirSync(audioUploadsDir);
    }
  }

  async generatePresignedUrl(fileType: string, isPrivate: boolean = false) {
    const bucket = isPrivate
      ? process.env.AWS_PRIVATE_BUCKET
      : process.env.AWS_BUCKET;

    const key = `${uuid()}-${Date.now()}`;

    const params = {
      Bucket: bucket,
      Key: key,
      ContentType: fileType,
      Expires: 300, // URL expires in 5 minutes
    };

    const uploadUrl = await this.s3.getSignedUrlPromise('putObject', params);

    return {
      uploadUrl,
      key,
      url: `https://${bucket}.s3.amazonaws.com/${key}`,
    };
  }

  handleFileUpload(file: Express.Multer.File, type: string) {
    const baseUrl = process.env.API_URL || 'http://localhost:3011';
    return {
      url: `${baseUrl}/uploads/${type}/${file.filename}`,
      filename: file.filename,
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
    };
  }

  async uploadFile(file: Express.Multer.File) {
    const uploadDir = './uploads';
    const maxFileSize = 5 * 1024 * 1024; // 5MB
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.mp3', '.wav', '.webm'];
    const ext = path.extname(file.originalname).toLowerCase();

    if (file.size > maxFileSize) {
      const { BadRequestException } = require('@nestjs/common');
      throw new BadRequestException('File is too large');
    }
    if (!allowedExtensions.includes(ext)) {
      const { BadRequestException } = require('@nestjs/common');
      throw new BadRequestException('File type not allowed');
    }

    await fs.promises.mkdir(uploadDir, { recursive: true });

    const filename = `${Date.now()}-${Math.random()}-${file.originalname}`;
    const filePath = path.join('uploads', filename); // Remove leading './' for filePath
    await fs.promises.writeFile(filePath, file.buffer);
    return {
      filename,
      path: filePath,
      url: `/uploads/${filename}`,
    };
  }

  async uploadAudio(file: Express.Multer.File) {
    const uploadDir = './uploads/audio';
    const maxFileSize = 10 * 1024 * 1024; // 10MB
    const allowedExtensions = ['.webm', '.mp3', '.wav', '.m4a', '.ogg'];
    const ext = path.extname(file.originalname).toLowerCase();

    if (!allowedExtensions.includes(ext)) {
      const { BadRequestException } = require('@nestjs/common');
      throw new BadRequestException('Only audio files are allowed!');
    }
    if (file.size > maxFileSize) {
      const { BadRequestException } = require('@nestjs/common');
      throw new BadRequestException('Audio file is too large');
    }

    await fs.promises.mkdir(uploadDir, { recursive: true });

    const filename = `${Date.now()}-${Math.random()}-${file.originalname}`;
    const filePath = path.join('uploads/audio', filename); // Remove leading './' for filePath
    await fs.promises.writeFile(filePath, file.buffer);
    return {
      filename,
      path: filePath,
      url: `/uploads/audio/${filename}`,
    };
  }

  async deleteFile(filename: string) {
    const uploadDir = './uploads';
    const filePath = path.join('uploads', filename); // Remove leading './' for filePath
    if (fs.existsSync(filePath)) {
      await fs.promises.unlink(filePath);
    }
  }
}