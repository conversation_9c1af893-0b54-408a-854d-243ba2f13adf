import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Summary, SummaryDocument } from './schemas/summary.schema';
import { Insight, InsightDocument } from './schemas/insight.schema';
import { AiPrompt, AiPromptDocument } from './schemas/ai-prompt.schema';
import { AiChat, AiChatDocument, ChatMessage } from './schemas/ai-chat.schema';
import { AppSettings, AppSettingsDocument } from './schemas/app-settings.schema';
import { CreateSummaryDto } from './dto/create-summary.dto';
import { CreateInsightDto } from './dto/create-insight.dto';
import { CreateAiPromptDto } from './dto/create-ai-prompt.dto';
import { CreateAiChatDto } from './dto/create-ai-chat.dto';
import { CreateAppSettingsDto } from './dto/create-app-settings.dto';
import { TranscribeAudioDto } from './dto/transcribe-audio.dto';
import { PostsService } from '../posts/posts.service';
import { PostDocument } from '../posts/schemas/post.schema'; // Added import
import { IAiProvider, MockAiProvider, OpenAiProvider } from './providers';

@Injectable()
export class AiService {
  constructor(
    @InjectModel(Summary.name) private summaryModel: Model<SummaryDocument>,
    @InjectModel(Insight.name) private insightModel: Model<InsightDocument>,
    @InjectModel(AiPrompt.name) private aiPromptModel: Model<AiPromptDocument>,
    @InjectModel(AiChat.name) private aiChatModel: Model<AiChatDocument>,
    @InjectModel(AppSettings.name) private appSettingsModel: Model<AppSettingsDocument>,
    private postsService: PostsService,
  ) {}

  private async getAiProvider(): Promise<IAiProvider | null> {
    const aiConfig = await this.getAiConfiguration();

    if (!aiConfig) {
      console.warn('AI configuration not found. Falling back to MockAiProvider.');
      return new MockAiProvider(); 
    }

    if (!aiConfig.provider || !aiConfig.apiKey || !aiConfig.model) {
      console.error('Incomplete AI configuration. Required fields (provider, apiKey, model) are missing. Falling back to MockAiProvider.');
      return new MockAiProvider(); 
    }

    switch (aiConfig.provider) {
      case 'openai':
      case 'azure_openai': 
        return new OpenAiProvider(aiConfig.apiKey, aiConfig.model, aiConfig.apiBaseUrl);
      case 'mock':
        return new MockAiProvider();
      default:
        console.warn(`Unsupported AI provider: ${aiConfig.provider}. Falling back to MockAiProvider.`);
        return new MockAiProvider();
    }
  }

  // Summary methods
  async createSummary(createSummaryDto: CreateSummaryDto): Promise<SummaryDocument> {
    const newSummary = new this.summaryModel(createSummaryDto);
    return newSummary.save();
  }

  async findAllSummaries(
    userId?: string,
    companyId?: string,
    tag?: string,
  ): Promise<SummaryDocument[]> {
    const query: any = {};
    if (userId) query.userId = userId;
    if (companyId) query.companyId = companyId;
    if (tag) query.tag = tag;
    return this.summaryModel.find(query).sort({ createdAt: -1 }).exec();
  }

  async findSummaryById(id: string): Promise<SummaryDocument> {
    const summary = await this.summaryModel.findById(id).exec();
    if (!summary) throw new NotFoundException(`Summary with ID ${id} not found`);
    return summary;
  }

  async updateSummary(id: string, updateSummaryDto: any): Promise<SummaryDocument> {
    const updatedSummary = await this.summaryModel.findByIdAndUpdate(id, updateSummaryDto, { new: true }).exec();
    if (!updatedSummary) throw new NotFoundException(`Summary with ID ${id} not found`);
    return updatedSummary;
  }

  async removeSummary(id: string): Promise<SummaryDocument> {
    const deletedSummary = await this.summaryModel.findByIdAndDelete(id).exec();
    if (!deletedSummary) throw new NotFoundException(`Summary with ID ${id} not found`);
    return deletedSummary;
  }

  async generateSummary(createSummaryDto: CreateSummaryDto): Promise<SummaryDocument | null> {
    const { userId, companyId, userPrompt, postIds } = createSummaryDto;

    try {
      const aiProvider = await this.getAiProvider();
      if (!aiProvider) {
        console.error(`Failed to get AI provider during summary generation.`);
        // Create a summary indicating provider failure
        const providerErrorSummaryDto: CreateSummaryDto = {
            ...createSummaryDto,
            summary: 'AI provider not available or configuration error.',
            lastUpdated: new Date(),
        };
        return this.createSummary(providerErrorSummaryDto);
      }
      
      let contentToSummarize = userPrompt || ""; // Initialize with userPrompt or empty string

      if (postIds && postIds.length > 0) {
        const posts: PostDocument[] = [];
        for (const postId of postIds) {
          try {
            const post = await this.postsService.findPostById(postId);
            if (post) {
              posts.push(post);
            } else {
              console.warn(`Post with ID ${postId} not found while generating summary.`);
            }
          } catch (err) {
            console.warn(`Error fetching post ${postId} for summary: ${err.message}`);
          }
        }

        if (posts.length === 0 && (!userPrompt || userPrompt.trim() === '')) {
           throw new BadRequestException('Cannot generate summary without posts or a prompt.');
        }

        const postContents = posts.map(p => `Post Title: ${p.body.substring(0,30)}...\nContent: ${p.body}`).join('\n\n---\n\n');
        
        if (userPrompt && userPrompt.trim() !== '') {
          contentToSummarize = `User Prompt: "${userPrompt}"\n\nPlease summarize the following content based on the prompt:\n\n${postContents}`;
        } else {
          contentToSummarize = `Please summarize the following content:\n\n${postContents}`;
        }
      } else if (!userPrompt || userPrompt.trim() === '') {
          throw new BadRequestException('Cannot generate summary without post IDs or a user prompt.');
      }

      const model = (await this.getAiConfiguration())?.model;
      const summaryText = await aiProvider.generateText(contentToSummarize, { model });

      if (!summaryText) {
        console.error('AI summary generation failed to produce text.');
        const errorSummaryDto: CreateSummaryDto = {
          ...createSummaryDto,
          summary: 'AI summary generation failed to produce text.',
          lastUpdated: new Date(),
        };
        return this.createSummary(errorSummaryDto);
      }

      const summaryDto: CreateSummaryDto = {
        ...createSummaryDto,
        summary: summaryText,
        lastUpdated: new Date(),
      };

      return this.createSummary(summaryDto);

    } catch (error) {
      console.error('Error generating summary:', error);
      if (error instanceof BadRequestException) throw error;
      
      const errorSummaryDto: CreateSummaryDto = {
          ...createSummaryDto,
          summary: `Failed to generate summary: ${error.message}`,
          lastUpdated: new Date(),
        };
      return this.createSummary(errorSummaryDto);
    }
  }

  // Insight methods
  async createInsight(createInsightDto: CreateInsightDto): Promise<InsightDocument> {
    const newInsight = new this.insightModel(createInsightDto);
    return newInsight.save();
  }

  async findAllInsights(
    userId?: string,
    companyId?: string,
    tag?: string,
  ): Promise<InsightDocument[]> {
    const query: any = {};
    if (userId) query.userId = userId;
    if (companyId) query.companyId = companyId;
    if (tag) query.tag = tag;
    return this.insightModel.find(query).sort({ createdAt: -1 }).exec();
  }

  async findInsightById(id: string): Promise<InsightDocument> {
    const insight = await this.insightModel.findById(id).exec();
    if (!insight) throw new NotFoundException(`Insight with ID ${id} not found`);
    return insight;
  }

  async updateInsight(id: string, updateInsightDto: any): Promise<InsightDocument> {
    const updatedInsight = await this.insightModel.findByIdAndUpdate(id, updateInsightDto, { new: true }).exec();
    if (!updatedInsight) throw new NotFoundException(`Insight with ID ${id} not found`);
    return updatedInsight;
  }

  async removeInsight(id: string): Promise<InsightDocument> {
    const deletedInsight = await this.insightModel.findByIdAndDelete(id).exec();
    if (!deletedInsight) throw new NotFoundException(`Insight with ID ${id} not found`);
    return deletedInsight;
  }

  async analyzeInsight(createInsightDto: CreateInsightDto): Promise<InsightDocument> {
    // This method is now superseded by analyzePost or should be adapted.
    // For now, let's keep its old behavior but note it might be deprecated or changed.
    console.warn("analyzeInsight method is being called, consider using analyzePost for specific post analysis.")
    const aiProvider = await this.getAiProvider();
    let insightText = 'Placeholder insight analysis: AI provider issue or failure.';

    if (aiProvider) {
      try {
        const model = (await this.getAiConfiguration())?.model;
        const generatedText = await aiProvider.generateText(createInsightDto.prompt || createInsightDto.title, { model });
        insightText = generatedText || 'AI failed to generate insight content.';
      } catch (error) {
        console.error('Error analyzing insight with AI provider:', error);
        insightText = 'Error during AI insight analysis.';
      }
    } else {
       console.error('Critical: AI Provider is null for insight analysis.');
    }
    
    createInsightDto.summary = insightText;
    return this.createInsight(createInsightDto);
  }

  async analyzePost(postId: string, userId: string, companyId?: string, analysisPrompt?: string): Promise<InsightDocument | null> {
    try {
      const post = await this.postsService.findPostById(postId);
      if (!post) {
        throw new NotFoundException(`Post with ID ${postId} not found.`);
      }

      const aiProvider = await this.getAiProvider(); // companyId for post can be post.companyId.toString()
      if (!aiProvider) {
        console.error(`Failed to get AI provider during post analysis.`);
        return null;
      }

      const defaultPrompt = `Analyze the following post content and provide insights:\n\n"${post.body}"`;
      const promptToUse = analysisPrompt || defaultPrompt;
      
      const model = (await this.getAiConfiguration())?.model;
      const analysisText = await aiProvider.generateText(promptToUse, { model });

      if (!analysisText) {
        console.error(`AI analysis failed to generate text for post ${postId}.`);
        return null;
      }

      const insightDto: CreateInsightDto = {
        title: `Analysis of Post: ${post.body.substring(0, 50)}...`,
        summary: analysisText,
        userId, 
        companyId: post.companyId?.toString(), 
        linkedObjectId: postId, 
        objectType: 'Post',    
        prompt: promptToUse,
        postId: postId, // explicitly set postId for the DTO
      };
      
      return this.createInsight(insightDto);

    } catch (error) {
      console.error(`Error analyzing post ${postId}:`, error);
      if (error instanceof NotFoundException) throw error; // Re-throw specific errors if needed by controller
      return null;
    }
  }

  // AI Prompt methods
  async createAiPrompt(createAiPromptDto: CreateAiPromptDto): Promise<AiPromptDocument> {
    const newAiPrompt = new this.aiPromptModel(createAiPromptDto);
    return newAiPrompt.save();
  }

  async findAllAiPrompts(
    companyId?: string,
    isSystem?: boolean,
  ): Promise<AiPromptDocument[]> {
    const query: any = {};
    if (companyId) query.companyId = companyId;
    if (isSystem !== undefined) query.isSystem = isSystem;
    return this.aiPromptModel.find(query).exec();
  }

  async findAiPromptById(id: string): Promise<AiPromptDocument> {
    const aiPrompt = await this.aiPromptModel.findById(id).exec();
    if (!aiPrompt) throw new NotFoundException(`AI Prompt with ID ${id} not found`);
    return aiPrompt;
  }

  async updateAiPrompt(id: string, updateAiPromptDto: any): Promise<AiPromptDocument> {
    const updatedAiPrompt = await this.aiPromptModel.findByIdAndUpdate(id, updateAiPromptDto, { new: true }).exec();
    if (!updatedAiPrompt) throw new NotFoundException(`AI Prompt with ID ${id} not found`);
    return updatedAiPrompt;
  }

  async removeAiPrompt(id: string): Promise<AiPromptDocument> {
    const deletedAiPrompt = await this.aiPromptModel.findByIdAndDelete(id).exec();
    if (!deletedAiPrompt) throw new NotFoundException(`AI Prompt with ID ${id} not found`);
    return deletedAiPrompt;
  }

  // AI Chat methods
  async createAiChat(createAiChatDto: CreateAiChatDto): Promise<AiChatDocument> {
    const newAiChat = new this.aiChatModel(createAiChatDto);
    return newAiChat.save();
  }

  async findAllAiChats(userId: string): Promise<AiChatDocument[]> {
    return this.aiChatModel.find({ userId }).sort({ updatedAt: -1 }).exec();
  }

  async findAiChatById(id: string): Promise<AiChatDocument> {
    const aiChat = await this.aiChatModel.findById(id).exec();
    if (!aiChat) throw new NotFoundException(`AI Chat with ID ${id} not found`);
    return aiChat;
  }

  async updateAiChat(id: string, updateAiChatDto: any): Promise<AiChatDocument> {
    const updatedAiChat = await this.aiChatModel.findByIdAndUpdate(id, updateAiChatDto, { new: true }).exec();
    if (!updatedAiChat) throw new NotFoundException(`AI Chat with ID ${id} not found`);
    return updatedAiChat;
  }

  async removeAiChat(id: string): Promise<AiChatDocument> {
    const deletedAiChat = await this.aiChatModel.findByIdAndDelete(id).exec();
    if (!deletedAiChat) throw new NotFoundException(`AI Chat with ID ${id} not found`);
    return deletedAiChat;
  }

  async sendChatMessage(chatId: string, message: string, userId: string): Promise<AiChatDocument> {
    const chat = await this.aiChatModel.findById(chatId).exec();
    if (!chat) throw new NotFoundException(`AI Chat with ID ${chatId} not found`);
    if (chat.userId.toString() !== userId) throw new ForbiddenException('You do not have permission to access this chat');

    const userMessage: ChatMessage = { role: 'user', content: message, timestamp: new Date() };
    chat.messages.push(userMessage);

    const aiProvider = await this.getAiProvider(); 

    if (aiProvider) {
      try {
        const model = (await this.getAiConfiguration())?.model;
        const aiResponseMessage = await aiProvider.createChatCompletion(chat.messages, { model });
        if (aiResponseMessage && aiResponseMessage.content) {
          chat.messages.push(aiResponseMessage);
        } else {
          console.warn(`AI Provider returned no content for chat ${chatId}. User message: "${message}"`);
        }
      } catch (error) {
        console.error('Error getting AI chat completion:', error);
      }
    } else {
      console.error(`Critical: AI Provider is null for chat ${chatId}.`);
    }
    
    const isNewChat = chat.messages.length <= (aiProvider ? 2 : 1);
    const isDefaultTitle = !chat.title || chat.title.startsWith('New Chat');
    if (isNewChat && isDefaultTitle) {
        chat.title = message.substring(0, 50) + (message.length > 50 ? '...' : '');
    }
    
    return chat.save();
  }

  // App Settings methods
  async createAppSettings(createAppSettingsDto: CreateAppSettingsDto): Promise<AppSettingsDocument> {
    const newAppSettings = new this.appSettingsModel(createAppSettingsDto);
    return newAppSettings.save();
  }

  async findAllAppSettings(
    type?: string,
    companyId?: string,
  ): Promise<AppSettingsDocument[]> {
    const query: any = {};
    if (type) query.type = type;
    if (companyId) query.companyId = companyId;
    return this.appSettingsModel.find(query).exec();
  }

  async getAiConfiguration(): Promise<any | null> {
    let configDoc: AppSettingsDocument | null = null;

    configDoc = await this.appSettingsModel.findOne({ type: 'AI_CONFIG', name: 'AI_CONFIG' }).exec();

    if (!configDoc || !configDoc.settings) return null;

    const configObject: any = {};
    const requiredKeys = ['provider', 'apiKey', 'model'];
    for (const setting of configDoc.settings) {
      configObject[setting.key] = setting.value;
    }

    for (const key of requiredKeys) {
      if (!(key in configObject)) {
        //console.error(`Missing required AI configuration key: ${key}`);
        return null; 
      }
    }
    
    if (!('apiBaseUrl' in configObject)) configObject.apiBaseUrl = null;
    return configObject;
  }

  async saveAiConfiguration(createAppSettingsDto: CreateAppSettingsDto): Promise<AppSettingsDocument> {
    const { userId, settings, name } = createAppSettingsDto;
    if (createAppSettingsDto.type !== 'AI_CONFIG') throw new BadRequestException('Type must be AI_CONFIG.');
    
    const requiredKeys = ['provider', 'apiKey', 'model'];
    const providedKeys = settings?.map(s => s.key) || [];
    for (const key of requiredKeys) {
      if (!providedKeys.includes(key)) throw new BadRequestException(`Missing required setting: ${key}.`);
    }

    const query: any = { type: 'AI_CONFIG', name: 'AI_CONFIG' };

    const existingSetting = await this.appSettingsModel.findOne(query).exec();

    if (existingSetting) {
      existingSetting.name = name;
      existingSetting.settings = settings || [];
      existingSetting.userId = userId as any; 
      existingSetting.markModified('settings');
      return existingSetting.save();
    } else {
      const newSetting = new this.appSettingsModel({ ...createAppSettingsDto, type: 'AI_CONFIG' });
      return newSetting.save();
    }
  }

  async findAppSettingsById(id: string): Promise<AppSettingsDocument> {
    const appSettings = await this.appSettingsModel.findById(id).exec();
    if (!appSettings) throw new NotFoundException(`App Settings with ID ${id} not found`);
    return appSettings;
  }

  async updateAppSettings(id: string, updateAppSettingsDto: any): Promise<AppSettingsDocument> {
    const updatedAppSettings = await this.appSettingsModel.findByIdAndUpdate(id, updateAppSettingsDto, { new: true }).exec();
    if (!updatedAppSettings) throw new NotFoundException(`App Settings with ID ${id} not found`);
    return updatedAppSettings;
  }

  async removeAppSettings(id: string): Promise<AppSettingsDocument> {
    const deletedAppSettings = await this.appSettingsModel.findByIdAndDelete(id).exec();
    if (!deletedAppSettings) throw new NotFoundException(`App Settings with ID ${id} not found`);
    return deletedAppSettings;
  }

  // Transcription method
  async transcribeAudio(transcribeAudioDto: TranscribeAudioDto): Promise<{ text: string }> {
    const { audioUrl, postTypeId } = transcribeAudioDto;
    const aiProvider = await this.getAiProvider();
    let transcriptionText = 'Transcription service unavailable or failed by default.';

    if (aiProvider) {
      try {
        const model = (await this.getAiConfiguration())?.model || 'whisper-1';
        const generatedText = await aiProvider.transcribe(audioUrl, { model });
        transcriptionText = generatedText !== null && generatedText !== undefined 
          ? generatedText 
          : "Audio transcription failed or returned empty.";
      } catch (error) {
        console.error('Error during audio transcription:', error);
        transcriptionText = "Error during audio transcription.";
      }
    } else {
      console.error('Critical: AI Provider is null for transcription request.');
    }

    if (postTypeId) {
         try {
            const postType = await this.postsService.findPostTypeById(postTypeId);
            if (postType) {
                transcriptionText += ` (Context: ${postType.name})`;
            }
        } catch (error) {
            console.error('Error finding post type during transcription enhancement:', error.message);
        }
    }

    return { text: transcriptionText };
  }
}
