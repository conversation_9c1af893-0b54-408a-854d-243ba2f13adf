import { Test, TestingModule } from '@nestjs/testing';
import { AiService } from './ai.service';
import { getModelToken } from '@nestjs/mongoose';
import { ConfigService } from '@nestjs/config';
import { Summary } from './schemas/summary.schema';
import { Insight } from './schemas/insight.schema';
import { AiPrompt } from './schemas/ai-prompt.schema';
import { AiChat } from './schemas/ai-chat.schema';
import { AppSettings } from './schemas/app-settings.schema';
import { PostsService } from '../posts/posts.service';
import { NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { Model } from 'mongoose';

describe('AiService', () => {
  let service: AiService;
  let summaryModel: Model<Summary>;
  let insightModel: Model<Insight>;
  let aiPromptModel: Model<AiPrompt>;
  let aiChatModel: Model<AiChat>;
  let appSettingsModel: Model<AppSettings>;
  let postsService: PostsService;
  // ConfigService is not used in the AiService methods being tested directly, so can be removed if not needed for other tests.
  // let configService: ConfigService;

  // Define types for model mocks for better type safety
  type MockModel<T = any> = {
    [K in keyof Model<T>]: jest.Mock;
  } & { new: jest.Mock; prototype: { save: jest.Mock } };


  const createMockModel = <T = any>(): MockModel<T> => ({
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(), // For instance method
    exec: jest.fn(),
    create: jest.fn(), // For static method
    new: jest.fn().mockImplementation(dto => ({ ...dto, save: jest.fn().mockResolvedValue({ ...dto, _id: 'mockId' }) })), // Mock constructor
    prototype: { save: jest.fn() } // Ensure prototype.save is also mocked for instances
  } as MockModel<T>);


  let mockSummaryModel: MockModel<Summary>;
  let mockInsightModel: MockModel<Insight>;
  let mockAiPromptModel: MockModel<AiPrompt>;
  let mockAiChatModel: MockModel<AiChat>;
  let mockAppSettingsModel: MockModel<AppSettings>;

  const mockPostsService = {
    findPostById: jest.fn(),
    // Add other methods if they become necessary for tests
  };

  // const mockConfigService = { // If ConfigService is needed
  //   get: jest.fn(),
  // };

  let consoleWarnSpy: jest.SpyInstance;
  let consoleErrorSpy: jest.SpyInstance;

  beforeEach(async () => {
    jest.clearAllMocks(); // Clears all mocks, including spies

    // Initialize mocks for each model
    mockSummaryModel = createMockModel<Summary>();
    mockInsightModel = createMockModel<Insight>();
    mockAiPromptModel = createMockModel<AiPrompt>();
    mockAiChatModel = createMockModel<AiChat>();
    mockAppSettingsModel = createMockModel<AppSettings>();
    
    // Spy on console methods before module compilation if possible, or right after
    consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});
    consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});


    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AiService,
        { provide: getModelToken(Summary.name), useValue: mockSummaryModel },
        { provide: getModelToken(Insight.name), useValue: mockInsightModel },
        { provide: getModelToken(AiPrompt.name), useValue: mockAiPromptModel },
        { provide: getModelToken(AiChat.name), useValue: mockAiChatModel },
        { provide: getModelToken(AppSettings.name), useValue: mockAppSettingsModel },
        { provide: PostsService, useValue: mockPostsService },
        // { provide: ConfigService, useValue: mockConfigService }, // If needed
      ],
    }).compile();

    service = module.get<AiService>(AiService);
    // Models are already mocked, direct assignment not needed here
    // summaryModel = module.get<Model<Summary>>(getModelToken(Summary.name));
    // ...etc.
    postsService = module.get<PostsService>(PostsService);
    // configService = module.get<ConfigService>(ConfigService); // If needed
  });

  afterEach(() => {
    // Restore console spies after each test
    consoleWarnSpy.mockRestore();
    consoleErrorSpy.mockRestore();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  // Placeholder for new test suites
  describe('getAiConfiguration', () => {
    const companyId = 'test-company-id';
    const globalConfigSettings = [
      { key: 'provider', value: 'openai', type: 'string' },
      { key: 'apiKey', value: 'global-key', type: 'string' },
      { key: 'model', value: 'gpt-3.5-turbo', type: 'string' },
      { key: 'apiBaseUrl', value: 'global-url', type: 'string' },
    ];
    const companyConfigSettings = [
      { key: 'provider', value: 'azure_openai', type: 'string' },
      { key: 'apiKey', value: 'company-key', type: 'string' },
      { key: 'model', value: 'gpt-4', type: 'string' },
    ];

    it('should return company-specific config if found', async () => {
      mockAppSettingsModel.findOne.mockReturnValueOnce({
        exec: jest.fn().mockResolvedValue({ _id: 'company-config-id', type: 'AI_CONFIG', companyId, settings: companyConfigSettings }),
      } as any); // Cast to any to satisfy exec chain

      const result = await service.getAiConfiguration(companyId);
      expect(mockAppSettingsModel.findOne).toHaveBeenCalledWith({ type: 'AI_CONFIG', companyId });
      expect(result).toEqual({
        provider: 'azure_openai',
        apiKey: 'company-key',
        model: 'gpt-4',
        apiBaseUrl: null, // Should default to null if not present
      });
    });

    it('should return global config if company-specific not found', async () => {
      mockAppSettingsModel.findOne
        .mockReturnValueOnce({ exec: jest.fn().mockResolvedValue(null) } as any) // Company config
        .mockReturnValueOnce({ 
          exec: jest.fn().mockResolvedValue({ _id: 'global-config-id', type: 'AI_CONFIG', settings: globalConfigSettings }) 
        } as any); // Global config

      const result = await service.getAiConfiguration(companyId);
      expect(mockAppSettingsModel.findOne).toHaveBeenCalledWith({ type: 'AI_CONFIG', companyId });
      expect(mockAppSettingsModel.findOne).toHaveBeenCalledWith({ type: 'AI_CONFIG', companyId: { $exists: false } });
      expect(result).toEqual({
        provider: 'openai',
        apiKey: 'global-key',
        model: 'gpt-3.5-turbo',
        apiBaseUrl: 'global-url',
      });
    });
    
    it('should return global config if companyId is not provided', async () => {
      mockAppSettingsModel.findOne.mockReturnValueOnce({ 
        exec: jest.fn().mockResolvedValue({ _id: 'global-config-id', type: 'AI_CONFIG', settings: globalConfigSettings }) 
      } as any);

      const result = await service.getAiConfiguration(); // No companyId
      expect(mockAppSettingsModel.findOne).toHaveBeenCalledWith({ type: 'AI_CONFIG', companyId: { $exists: false } });
      expect(result).toEqual({
        provider: 'openai',
        apiKey: 'global-key',
        model: 'gpt-3.5-turbo',
        apiBaseUrl: 'global-url',
      });
    });

    it('should return null if no config is found', async () => {
      mockAppSettingsModel.findOne.mockReturnValue({ exec: jest.fn().mockResolvedValue(null) } as any);
      const result = await service.getAiConfiguration(companyId);
      expect(result).toBeNull();
    });

    it('should return null and log warning if config is incomplete (missing provider)', async () => {
      const incompleteSettings = [{ key: 'apiKey', value: 'key', type: 'string' }, { key: 'model', value: 'model', type: 'string' }];
      mockAppSettingsModel.findOne.mockReturnValueOnce({ 
        exec: jest.fn().mockResolvedValue({ _id: 'incomplete-id', type: 'AI_CONFIG', settings: incompleteSettings }) 
      } as any);
      
      const result = await service.getAiConfiguration();
      expect(result).toBeNull();
      expect(consoleWarnSpy).toHaveBeenCalledWith(expect.stringContaining('is missing required setting: provider'));
    });
    
    it('should return null and log warning if config is incomplete (missing apiKey)', async () => {
      const incompleteSettings = [{ key: 'provider', value: 'prov', type: 'string' }, { key: 'model', value: 'model', type: 'string' }];
      mockAppSettingsModel.findOne.mockReturnValueOnce({ 
        exec: jest.fn().mockResolvedValue({ _id: 'incomplete-id', type: 'AI_CONFIG', settings: incompleteSettings }) 
      } as any);
      
      const result = await service.getAiConfiguration();
      expect(result).toBeNull();
      expect(consoleWarnSpy).toHaveBeenCalledWith(expect.stringContaining('is missing required setting: apiKey'));
    });

    it('should return null and log warning if config is incomplete (missing model)', async () => {
      const incompleteSettings = [{ key: 'provider', value: 'prov', type: 'string' }, { key: 'apiKey', value: 'key', type: 'string' }];
      mockAppSettingsModel.findOne.mockReturnValueOnce({ 
        exec: jest.fn().mockResolvedValue({ _id: 'incomplete-id', type: 'AI_CONFIG', settings: incompleteSettings }) 
      } as any);
      
      const result = await service.getAiConfiguration();
      expect(result).toBeNull();
      expect(consoleWarnSpy).toHaveBeenCalledWith(expect.stringContaining('is missing required setting: model'));
    });
  });

  describe('saveAiConfiguration', () => {
    const userId = 'test-user';
    const companyId = 'test-company';
    const validSettings = [
      { key: 'provider', value: 'openai', type: 'string' },
      { key: 'apiKey', value: 'some-key', type: 'string' },
      { key: 'model', value: 'gpt-3.5-turbo', type: 'string' },
    ];
    const createAppSettingsDto = {
      type: 'AI_CONFIG',
      name: 'Test AI Config',
      userId,
      settings: validSettings,
    };

    it('should create new global AI config if none exists', async () => {
      mockAppSettingsModel.findOne.mockReturnValue({ exec: jest.fn().mockResolvedValue(null) } as any);
      const saveMock = jest.fn().mockResolvedValue({ ...createAppSettingsDto, _id: 'new-id' });
      // mockAppSettingsModel.create.mockResolvedValue({ ...createAppSettingsDto, _id: 'new-id' } as any);
      // For `new this.appSettingsModel(dto).save()`
      mockAppSettingsModel.new.mockImplementation((dto) => ({ ...dto, save: saveMock }));


      const result = await service.saveAiConfiguration({ ...createAppSettingsDto, companyId: undefined });
      
      expect(mockAppSettingsModel.findOne).toHaveBeenCalledWith({ type: 'AI_CONFIG', companyId: { $exists: false } });
      // expect(mockAppSettingsModel.create).toHaveBeenCalledWith(expect.objectContaining({ type: 'AI_CONFIG', companyId: undefined }));
      expect(mockAppSettingsModel.new).toHaveBeenCalledWith(expect.objectContaining({ type: 'AI_CONFIG', companyId: undefined }));
      expect(saveMock).toHaveBeenCalled();
      expect(result).toHaveProperty('_id', 'new-id');
    });

    it('should create new company-specific AI config if none exists', async () => {
      mockAppSettingsModel.findOne.mockReturnValue({ exec: jest.fn().mockResolvedValue(null) } as any);
      const saveMock = jest.fn().mockResolvedValue({ ...createAppSettingsDto, companyId, _id: 'new-company-id' });
      mockAppSettingsModel.new.mockImplementation((dto) => ({ ...dto, save: saveMock }));

      const result = await service.saveAiConfiguration({ ...createAppSettingsDto, companyId });
      
      expect(mockAppSettingsModel.findOne).toHaveBeenCalledWith({ type: 'AI_CONFIG', companyId });
      expect(mockAppSettingsModel.new).toHaveBeenCalledWith(expect.objectContaining({ type: 'AI_CONFIG', companyId }));
      expect(saveMock).toHaveBeenCalled();
      expect(result).toHaveProperty('_id', 'new-company-id');
    });

    it('should update existing AI config', async () => {
      const existingConfig = { 
        ...createAppSettingsDto, 
        companyId, 
        _id: 'existing-id', 
        save: jest.fn().mockResolvedValue({ ...createAppSettingsDto, companyId, _id: 'existing-id', name: 'Updated Name' }),
        markModified: jest.fn(),
      };
      mockAppSettingsModel.findOne.mockReturnValue({ exec: jest.fn().mockResolvedValue(existingConfig) } as any);

      const result = await service.saveAiConfiguration({ ...createAppSettingsDto, companyId, name: 'Updated Name' });
      
      expect(existingConfig.save).toHaveBeenCalled();
      expect(existingConfig.markModified).toHaveBeenCalledWith('settings');
      expect(result.name).toBe('Updated Name');
    });

    it('should throw BadRequestException if type is not AI_CONFIG', async () => {
      await expect(service.saveAiConfiguration({ ...createAppSettingsDto, type: 'WRONG_TYPE' } as any))
        .rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException if required settings are missing (e.g. provider)', async () => {
      const incompleteSettings = [
        { key: 'apiKey', value: 'key', type: 'string' },
        { key: 'model', value: 'model', type: 'string' },
      ];
      await expect(service.saveAiConfiguration({ ...createAppSettingsDto, settings: incompleteSettings }))
        .rejects.toThrow(new BadRequestException('Missing required setting: provider in AI configuration.'));
    });
  });
  describe('getAiProvider', () => { /* Tests will go here */ });
  describe('generateSummary (New Implementation)', () => { /* Tests will go here */ });
  describe('analyzeInsight (Potentially Deprecated/Changed)', () => { 
    // This test suite can be minimal if the method is truly deprecated or just ensure it calls getAiProvider
    it('should call getAiProvider and generate text for legacy analyzeInsight', async () => {
      const dto = { title: 'Legacy Insight', userId: 'user1', companyId: 'comp1', prompt: 'Analyze this legacy' };
      const mockProvider = { generateText: jest.fn().mockResolvedValue('Legacy analysis') } as any;
      jest.spyOn(service as any, 'getAiProvider').mockResolvedValue(mockProvider); // Spy on private method
      mockInsightModel.new.mockImplementation(d => ({ ...d, save: jest.fn().mockResolvedValue(d) }));


      await service.analyzeInsight(dto);
      expect(service['getAiProvider']).toHaveBeenCalledWith('comp1');
      expect(mockProvider.generateText).toHaveBeenCalledWith('Analyze this legacy', expect.any(Object));
      expect(mockInsightModel.new).toHaveBeenCalled();
      expect(consoleWarnSpy).toHaveBeenCalledWith(expect.stringContaining("analyzeInsight method is being called"));
    });
   });
  describe('getAiProvider', () => {
    let getAiConfigurationSpy: jest.SpyInstance;

    beforeEach(() => {
      // Spy on the actual getAiConfiguration method of the service instance
      getAiConfigurationSpy = jest.spyOn(service, 'getAiConfiguration' as any);
    });

    afterEach(() => {
      getAiConfigurationSpy.mockRestore();
    });

    it('should return OpenAiProvider for "openai" config', async () => {
      getAiConfigurationSpy.mockResolvedValue({ provider: 'openai', apiKey: 'key', model: 'model' });
      const provider = await (service as any).getAiProvider('company1');
      expect(provider).toBeInstanceOf(OpenAiProvider);
    });
    
    it('should return OpenAiProvider for "azure_openai" config', async () => {
      getAiConfigurationSpy.mockResolvedValue({ provider: 'azure_openai', apiKey: 'key', model: 'model' });
      const provider = await (service as any).getAiProvider('company1');
      expect(provider).toBeInstanceOf(OpenAiProvider);
    });

    it('should return MockAiProvider for "mock" config', async () => {
      getAiConfigurationSpy.mockResolvedValue({ provider: 'mock', apiKey: 'key', model: 'model' });
      const provider = await (service as any).getAiProvider('company1');
      expect(provider).toBeInstanceOf(MockAiProvider);
    });

    it('should return MockAiProvider and log warning if config not found', async () => {
      getAiConfigurationSpy.mockResolvedValue(null);
      const provider = await (service as any).getAiProvider('company1');
      expect(provider).toBeInstanceOf(MockAiProvider);
      expect(consoleWarnSpy).toHaveBeenCalledWith('AI configuration not found. Falling back to MockAiProvider.');
    });

    it('should return MockAiProvider and log error if config is incomplete (missing apiKey)', async () => {
      getAiConfigurationSpy.mockResolvedValue({ provider: 'openai', model: 'model' }); // Missing apiKey
      const provider = await (service as any).getAiProvider('company1');
      expect(provider).toBeInstanceOf(MockAiProvider);
      expect(consoleErrorSpy).toHaveBeenCalledWith('Incomplete AI configuration. Required fields (provider, apiKey, model) are missing. Falling back to MockAiProvider.');
    });
    
    it('should return MockAiProvider and log error if config is incomplete (missing provider)', async () => {
      getAiConfigurationSpy.mockResolvedValue({ apiKey: 'key', model: 'model' }); // Missing provider
      const provider = await (service as any).getAiProvider('company1');
      expect(provider).toBeInstanceOf(MockAiProvider);
      expect(consoleErrorSpy).toHaveBeenCalledWith('Incomplete AI configuration. Required fields (provider, apiKey, model) are missing. Falling back to MockAiProvider.');
    });

    it('should return MockAiProvider and log warning for unsupported provider', async () => {
      getAiConfigurationSpy.mockResolvedValue({ provider: 'unsupported', apiKey: 'key', model: 'model' });
      const provider = await (service as any).getAiProvider('company1');
      expect(provider).toBeInstanceOf(MockAiProvider);
      expect(consoleWarnSpy).toHaveBeenCalledWith('Unsupported AI provider: unsupported. Falling back to MockAiProvider.');
    });
  });

  describe('generateSummary (New Implementation)', () => {
    let mockAiProvider: jest.Mocked<IAiProvider>;
    let getAiProviderSpy: jest.SpyInstance;

    beforeEach(() => {
      mockAiProvider = {
        generateText: jest.fn(),
        createChatCompletion: jest.fn(),
        transcribe: jest.fn(),
      };
      getAiProviderSpy = jest.spyOn(service as any, 'getAiProvider').mockResolvedValue(mockAiProvider);
    });

    afterEach(() => {
      getAiProviderSpy.mockRestore();
    });

    const createSummaryDtoBase = { userId: 'user1', companyId: 'comp1' };

    it('should generate summary using userPrompt if no postIds provided', async () => {
      const userPrompt = 'Summarize this general topic.';
      mockAiProvider.generateText.mockResolvedValue('General summary text.');
      mockSummaryModel.new.mockImplementation(dto => ({ ...dto, save: jest.fn().mockResolvedValue({ ...dto, _id: 'summary-id' }) }));


      const result = await service.generateSummary({ ...createSummaryDtoBase, userPrompt });

      expect(getAiProviderSpy).toHaveBeenCalledWith('comp1');
      expect(mockAiProvider.generateText).toHaveBeenCalledWith(userPrompt, expect.any(Object)); // Model might be passed
      expect(mockSummaryModel.new).toHaveBeenCalledWith(expect.objectContaining({ summary: 'General summary text.' }));
      expect(result).toHaveProperty('_id', 'summary-id');
    });

    it('should fetch posts and generate summary if postIds are provided', async () => {
      const postIds = ['post1', 'post2'];
      const mockPosts = [
        { _id: 'post1', companyId: 'comp1', body: 'Content of post 1' },
        { _id: 'post2', companyId: 'comp1', body: 'Content of post 2' },
      ];
      mockPostsService.findPostById
        .mockResolvedValueOnce(mockPosts[0])
        .mockResolvedValueOnce(mockPosts[1]);
      mockAiProvider.generateText.mockResolvedValue('Summary of posts.');
      mockSummaryModel.new.mockImplementation(dto => ({ ...dto, save: jest.fn().mockResolvedValue({ ...dto, _id: 'summary-posts-id' }) }));

      const result = await service.generateSummary({ ...createSummaryDtoBase, postIds });
      
      const expectedContentToSummarize = `Please summarize the following content:\n\nPost Title: ${mockPosts[0].body.substring(0,30)}...\nContent: ${mockPosts[0].body}\n\n---\n\nPost Title: ${mockPosts[1].body.substring(0,30)}...\nContent: ${mockPosts[1].body}`;

      expect(mockPostsService.findPostById).toHaveBeenCalledWith('post1');
      expect(mockPostsService.findPostById).toHaveBeenCalledWith('post2');
      expect(mockAiProvider.generateText).toHaveBeenCalledWith(expectedContentToSummarize, expect.any(Object));
      expect(mockSummaryModel.new).toHaveBeenCalledWith(expect.objectContaining({ summary: 'Summary of posts.' }));
      expect(result).toHaveProperty('_id', 'summary-posts-id');
    });
    
    it('should combine userPrompt and postIds content for summary generation', async () => {
      const userPrompt = "Focus on financial aspects.";
      const postIds = ['post1'];
      const mockPost = { _id: 'post1', companyId: 'comp1', body: 'Financial data and other details.' };
      mockPostsService.findPostById.mockResolvedValueOnce(mockPost);
      mockAiProvider.generateText.mockResolvedValue('Financial summary.');
      mockSummaryModel.new.mockImplementation(dto => ({ ...dto, save: jest.fn().mockResolvedValue({ ...dto, _id: 'summary-combo-id' }) }));

      await service.generateSummary({ ...createSummaryDtoBase, userPrompt, postIds });

      const expectedContent = `User Prompt: "${userPrompt}"\n\nPlease summarize the following content based on the prompt:\n\nPost Title: ${mockPost.body.substring(0,30)}...\nContent: ${mockPost.body}`;
      expect(mockAiProvider.generateText).toHaveBeenCalledWith(expectedContent, expect.any(Object));
    });

    it('should throw BadRequestException if no postIds and no userPrompt', async () => {
      await expect(service.generateSummary({ ...createSummaryDtoBase })).rejects.toThrow(BadRequestException);
    });
    
    it('should throw BadRequestException if postIds are empty and no userPrompt', async () => {
      await expect(service.generateSummary({ ...createSummaryDtoBase, postIds: [] })).rejects.toThrow(BadRequestException);
    });

    it('should handle AI provider failure gracefully by creating an error summary', async () => {
      mockAiProvider.generateText.mockRejectedValue(new Error('AI exploded'));
      mockSummaryModel.new.mockImplementation(dto => ({ ...dto, save: jest.fn().mockResolvedValue({ ...dto, _id: 'summary-error-id' }) }));

      const result = await service.generateSummary({ ...createSummaryDtoBase, userPrompt: "Test" });

      expect(result?.summary).toContain('Failed to generate summary: AI exploded');
    });
    
    it('should handle case where AI provider returns null/undefined by creating an error summary', async () => {
        mockAiProvider.generateText.mockResolvedValue(null); // Simulate AI returning nothing
        mockSummaryModel.new.mockImplementation(dto => ({ ...dto, save: jest.fn().mockResolvedValue({ ...dto, _id: 'summary-null-ai-id' }) }));

        const result = await service.generateSummary({ ...createSummaryDtoBase, userPrompt: "Summarize this please" });
        
        expect(mockSummaryModel.new).toHaveBeenCalledWith(expect.objectContaining({
            summary: 'AI summary generation failed to produce text.',
        }));
        expect(result).toHaveProperty('_id', 'summary-null-ai-id');
    });


    it('should handle posts not found gracefully when generating summary with postIds', async () => {
        const postIds = ['post1', 'nonexistentPost'];
        mockPostsService.findPostById
            .mockResolvedValueOnce({ _id: 'post1', companyId: 'comp1', body: 'Content of post 1' })
            .mockResolvedValueOnce(null); // Second post not found
        mockAiProvider.generateText.mockResolvedValue('Summary of existing post.');
        mockSummaryModel.new.mockImplementation(dto => ({ ...dto, save: jest.fn().mockResolvedValue({ ...dto, _id: 'summary-partial-id' }) }));

        await service.generateSummary({ ...createSummaryDtoBase, postIds });

        expect(consoleWarnSpy).toHaveBeenCalledWith('Post with ID nonexistentPost not found while generating summary.');
        const expectedContentToSummarize = `Please summarize the following content:\n\nPost Title: Content of post 1...\nContent: Content of post 1`;
        expect(mockAiProvider.generateText).toHaveBeenCalledWith(expectedContentToSummarize, expect.any(Object));
    });
  });

  describe('analyzePost', () => {
    let mockAiProvider: jest.Mocked<IAiProvider>;
    let getAiProviderSpy: jest.SpyInstance;

    beforeEach(() => {
      mockAiProvider = {
        generateText: jest.fn(),
        createChatCompletion: jest.fn(),
        transcribe: jest.fn(),
      };
      getAiProviderSpy = jest.spyOn(service as any, 'getAiProvider').mockResolvedValue(mockAiProvider);
      mockInsightModel.new.mockImplementation(dto => ({ ...dto, save: jest.fn().mockResolvedValue({ ...dto, _id: 'insight-id' }) }));

    });
    
    afterEach(() => {
      getAiProviderSpy.mockRestore();
    });

    const postId = 'test-post-id';
    const userId = 'test-user-id';
    const companyId = 'test-company-id';
    const mockPost = { _id: postId, body: 'This is a test post content.', companyId: companyId };

    it('should analyze a post and create an insight', async () => {
      mockPostsService.findPostById.mockResolvedValue(mockPost);
      mockAiProvider.generateText.mockResolvedValue('Insightful analysis of the post.');

      const result = await service.analyzePost(postId, userId, companyId);

      expect(mockPostsService.findPostById).toHaveBeenCalledWith(postId);
      expect(getAiProviderSpy).toHaveBeenCalledWith(companyId);
      expect(mockAiProvider.generateText).toHaveBeenCalledWith(
        `Analyze the following post content and provide insights:\n\n"${mockPost.body}"`,
        expect.any(Object) // Model may be passed
      );
      expect(mockInsightModel.new).toHaveBeenCalledWith(expect.objectContaining({
        title: `Analysis of Post: ${mockPost.body.substring(0, 50)}...`,
        summary: 'Insightful analysis of the post.',
        userId,
        companyId,
        linkedObjectId: postId,
        objectType: 'Post',
        postId: postId,
      }));
      expect(result).toHaveProperty('_id', 'insight-id');
    });

    it('should use custom prompt if provided', async () => {
      const customPrompt = 'Custom analysis prompt.';
      mockPostsService.findPostById.mockResolvedValue(mockPost);
      mockAiProvider.generateText.mockResolvedValue('Custom analysis result.');

      await service.analyzePost(postId, userId, companyId, customPrompt);
      expect(mockAiProvider.generateText).toHaveBeenCalledWith(customPrompt, expect.any(Object));
    });

    it('should throw NotFoundException if post not found', async () => {
      mockPostsService.findPostById.mockResolvedValue(null);
      await expect(service.analyzePost(postId, userId, companyId)).rejects.toThrow(NotFoundException);
    });
    
    it('should return null if AI provider is not available (mocking getAiProvider to return null)', async () => {
      mockPostsService.findPostById.mockResolvedValue(mockPost);
      getAiProviderSpy.mockResolvedValue(null); // Override to simulate null provider

      const result = await service.analyzePost(postId, userId, companyId);
      expect(result).toBeNull();
      expect(consoleErrorSpy).toHaveBeenCalledWith(expect.stringContaining(`Failed to get AI provider for company ${companyId}`));
    });

    it('should return null if AI generation fails', async () => {
      mockPostsService.findPostById.mockResolvedValue(mockPost);
      mockAiProvider.generateText.mockResolvedValue(null); // AI returns no text

      const result = await service.analyzePost(postId, userId, companyId);
      expect(result).toBeNull();
      expect(consoleErrorSpy).toHaveBeenCalledWith(`AI analysis failed to generate text for post ${postId}.`);
    });
    
    it('should return null and log error if AI provider throws an error', async () => {
      mockPostsService.findPostById.mockResolvedValue(mockPost);
      const aiError = new Error('AI provider exploded');
      mockAiProvider.generateText.mockRejectedValue(aiError);

      const result = await service.analyzePost(postId, userId, companyId);
      
      expect(result).toBeNull();
      expect(consoleErrorSpy).toHaveBeenCalledWith(`Error analyzing post ${postId}:`, aiError);
    });
  });

  describe('sendChatMessage (Updated)', () => {
    let mockAiProvider: jest.Mocked<IAiProvider>;
    let getAiProviderSpy: jest.SpyInstance;
    const chatId = 'chat-id-123';
    const userId = 'user-id-456';
    const companyId = 'company-id-789'; // Assuming chat might have companyId
    const initialMessageContent = 'Hello AI!';

    beforeEach(() => {
      mockAiProvider = {
        generateText: jest.fn(),
        createChatCompletion: jest.fn(),
        transcribe: jest.fn(),
      };
      getAiProviderSpy = jest.spyOn(service as any, 'getAiProvider').mockResolvedValue(mockAiProvider);
    });

    afterEach(() => {
      getAiProviderSpy.mockRestore();
    });

    it('should add user message, get AI response, and save chat', async () => {
      const mockChat = {
        _id: chatId,
        userId,
        companyId,
        messages: [],
        title: 'New Chat',
        save: jest.fn().mockImplementation(function() { return Promise.resolve(this); }), // 'this' refers to the mockChat object itself
      };
      mockAiChatModel.findById.mockReturnValue({ exec: jest.fn().mockResolvedValue(mockChat) } as any);
      const aiResponseContent = 'Hello there, user!';
      mockAiProvider.createChatCompletion.mockResolvedValue({
        role: 'assistant',
        content: aiResponseContent,
        timestamp: new Date(),
      });

      const result = await service.sendChatMessage(chatId, initialMessageContent, userId);

      expect(mockAiChatModel.findById).toHaveBeenCalledWith(chatId);
      expect(getAiProviderSpy).toHaveBeenCalledWith(companyId);
      expect(mockChat.messages.length).toBe(2); // User message + AI message
      expect(mockChat.messages[0].content).toBe(initialMessageContent);
      expect(mockChat.messages[1].content).toBe(aiResponseContent);
      expect(mockAiProvider.createChatCompletion).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({ role: 'user', content: initialMessageContent }),
        ]),
        expect.any(Object) // Model might be passed
      );
      expect(mockChat.save).toHaveBeenCalled();
      expect(result.title).toBe(initialMessageContent.substring(0, 50)); // Title updated
    });

    it('should throw NotFoundException if chat not found', async () => {
      mockAiChatModel.findById.mockReturnValue({ exec: jest.fn().mockResolvedValue(null) } as any);
      await expect(service.sendChatMessage(chatId, initialMessageContent, userId))
        .rejects.toThrow(NotFoundException);
    });

    it('should throw ForbiddenException if user does not own chat', async () => {
      const mockChat = { _id: chatId, userId: 'another-user-id', messages: [] };
      mockAiChatModel.findById.mockReturnValue({ exec: jest.fn().mockResolvedValue(mockChat) } as any);
      await expect(service.sendChatMessage(chatId, initialMessageContent, userId))
        .rejects.toThrow(ForbiddenException);
    });
    
    it('should handle AI provider returning null for chat completion gracefully', async () => {
      const mockChat = {
        _id: chatId, userId, companyId, messages: [], title: 'New Chat',
        save: jest.fn().mockImplementation(function() { return Promise.resolve(this); }),
      };
      mockAiChatModel.findById.mockReturnValue({ exec: jest.fn().mockResolvedValue(mockChat) } as any);
      mockAiProvider.createChatCompletion.mockResolvedValue(null); // AI returns no response

      const result = await service.sendChatMessage(chatId, initialMessageContent, userId);
      
      expect(mockChat.messages.length).toBe(1); // Only user message
      expect(mockChat.messages[0].content).toBe(initialMessageContent);
      expect(consoleWarnSpy).toHaveBeenCalledWith(expect.stringContaining(`AI Provider returned no content for chat ${chatId}`));
      expect(result.title).toBe(initialMessageContent.substring(0,50)); // Title should still update
      expect(mockChat.save).toHaveBeenCalled();
    });

    it('should handle AI provider throwing an error gracefully', async () => {
        const mockChat = {
            _id: chatId, userId, companyId, messages: [], title: 'New Chat',
            save: jest.fn().mockImplementation(function() { return Promise.resolve(this); }),
        };
        mockAiChatModel.findById.mockReturnValue({ exec: jest.fn().mockResolvedValue(mockChat) } as any);
        mockAiProvider.createChatCompletion.mockRejectedValue(new Error("AI network error"));

        const result = await service.sendChatMessage(chatId, initialMessageContent, userId);
        
        expect(mockChat.messages.length).toBe(1); // Only user message
        expect(consoleErrorSpy).toHaveBeenCalledWith('Error getting AI chat completion:', expect.any(Error));
        expect(result.title).toBe(initialMessageContent.substring(0,50));
        expect(mockChat.save).toHaveBeenCalled();
    });
  });

  describe('transcribeAudio (Updated)', () => {
    let mockAiProvider: jest.Mocked<IAiProvider>;
    let getAiProviderSpy: jest.SpyInstance;
    const audioUrl = 'http://example.com/audio.wav';
    const companyId = 'company-transcribe';
    const postTypeId = 'post-type-transcribe';

    beforeEach(() => {
      mockAiProvider = {
        generateText: jest.fn(),
        createChatCompletion: jest.fn(),
        transcribe: jest.fn(),
      };
      getAiProviderSpy = jest.spyOn(service as any, 'getAiProvider').mockResolvedValue(mockAiProvider);
    });
    
    afterEach(() => {
      getAiProviderSpy.mockRestore();
    });

    const transcribeDtoBase: TranscribeAudioDto = { audioUrl, companyId, postTypeId, language: 'en' };

    it('should transcribe audio and append postType context', async () => {
      const transcription = 'This is the transcribed text.';
      mockAiProvider.transcribe.mockResolvedValue(transcription);
      const mockPostType = { name: 'Meeting Notes' };
      mockPostsService.findPostTypeById.mockResolvedValue(mockPostType as any);

      const result = await service.transcribeAudio(transcribeDtoBase);

      expect(getAiProviderSpy).toHaveBeenCalledWith(companyId);
      expect(mockAiProvider.transcribe).toHaveBeenCalledWith(audioUrl, { language: 'en', model: 'whisper-1' });
      expect(mockPostsService.findPostTypeById).toHaveBeenCalledWith(postTypeId);
      expect(result.text).toBe(`${transcription} (Context: ${mockPostType.name})`);
    });
    
    it('should transcribe audio without postType context if postTypeId is not provided', async () => {
      const transcription = 'Raw transcription.';
      mockAiProvider.transcribe.mockResolvedValue(transcription);

      const result = await service.transcribeAudio({ audioUrl, companyId, language: 'es' }); // No postTypeId

      expect(mockAiProvider.transcribe).toHaveBeenCalledWith(audioUrl, { language: 'es', model: 'whisper-1' });
      expect(mockPostsService.findPostTypeById).not.toHaveBeenCalled();
      expect(result.text).toBe(transcription);
    });
    
    it('should handle AI provider returning null for transcription', async () => {
      mockAiProvider.transcribe.mockResolvedValue(null);
      const mockPostType = { name: 'Meeting Notes' }; // Still try to get post type
      mockPostsService.findPostTypeById.mockResolvedValue(mockPostType as any);


      const result = await service.transcribeAudio(transcribeDtoBase);
      
      expect(consoleWarnSpy).toHaveBeenCalledWith(`AI Provider returned no transcription for audio URL: ${audioUrl}`);
      expect(result.text).toBe('Audio transcription failed or returned empty. (Context: Meeting Notes)');
    });

    it('should handle AI provider throwing an error during transcription', async () => {
      mockAiProvider.transcribe.mockRejectedValue(new Error('Transcription failed'));
      const mockPostType = { name: 'Meeting Notes' };
      mockPostsService.findPostTypeById.mockResolvedValue(mockPostType as any);


      const result = await service.transcribeAudio(transcribeDtoBase);
      
      expect(consoleErrorSpy).toHaveBeenCalledWith('Error during audio transcription:', expect.any(Error));
      expect(result.text).toBe('Error during audio transcription. (Context: Meeting Notes)');
    });
    
    it('should handle PostsService throwing an error for postType lookup', async () => {
      const transcription = 'Base transcription.';
      mockAiProvider.transcribe.mockResolvedValue(transcription);
      mockPostsService.findPostTypeById.mockRejectedValue(new Error('PostType service down'));

      const result = await service.transcribeAudio(transcribeDtoBase);

      expect(result.text).toBe(transcription); // Original transcription should still be there
      expect(consoleErrorSpy).toHaveBeenCalledWith('Error finding post type during transcription enhancement:', 'PostType service down');
    });
  });

  // Keep existing tests if they are still relevant or adapt them
  // For example, the old generateSummary tests might need to be removed or heavily adapted.
  // The sendChatMessage tests will also need adaptation.
  // The transcribeAudio tests will need adaptation.

  // Example of how an existing test might be removed or commented out if fully replaced:
  // describe('generateSummary (Old)', () => {
  //   it('OLD: should generate a summary and save it', async () => {
  //     // ... old test logic ...
  //   });
  // });
  
});
