import { MongoClient } from 'mongodb';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function updatePostTypes() {
  const uri = process.env.MONGODB_URI || '';
  if (!uri) {
    console.error('MongoDB URI not found in environment variables');
    process.exit(1);
  }

  const client = new MongoClient(uri);

  try {
    await client.connect();
    console.log('Connected to MongoDB');

    const database = client.db();
    const postTypesCollection = database.collection('kd_post_types');

    // Update the schema to include recordTips field
    const result = await postTypesCollection.updateMany(
      { recordTips: { $exists: false } },
      { $set: { recordTips: '' } }
    );

    console.log(`Updated ${result.modifiedCount} post types to include recordTips field`);
  } catch (error) {
    console.error('Error updating post types:', error);
  } finally {
    await client.close();
    console.log('Disconnected from MongoDB');
  }
}

// Run the migration
updatePostTypes().catch(console.error);
