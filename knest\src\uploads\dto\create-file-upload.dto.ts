import { IsNotEmpty, <PERSON>String, <PERSON><PERSON><PERSON>ber, <PERSON>Optional, IsE<PERSON> } from 'class-validator';

export class CreateFileUploadDto {
  @IsNotEmpty()
  @IsString()
  originalName: string;

  @IsNotEmpty()
  @IsString()
  mimeType: string;

  @IsNotEmpty()
  @IsNumber()
  size: number;

  @IsNotEmpty()
  @IsString()
  key: string;

  @IsNotEmpty()
  @IsString()
  url: string;

  @IsNotEmpty()
  @IsEnum(['avatar', 'logo', 'audio', 'document', 'other'])
  type: string;

  @IsNotEmpty()
  @IsString()
  userId: string;

  @IsOptional()
  @IsString()
  linkedObjectId?: string;

  @IsOptional()
  @IsEnum(['Post', 'User', 'Company', 'Insight'])
  objectType?: string;
}
