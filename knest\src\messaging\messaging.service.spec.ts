import { Test, TestingModule } from '@nestjs/testing';
import { MessagingService } from './messaging.service';
import { getModelToken } from '@nestjs/mongoose';
import { Conversation } from './schemas/conversation.schema';
import { Message } from './schemas/message.schema';
import { UsersService } from '../users/users.service';
import { NotFoundException } from '@nestjs/common';
import { Model } from 'mongoose';

describe('MessagingService', () => {
  let service: MessagingService;
  let conversationModel: Model<Conversation>;
  let messageModel: Model<Message>;
  let usersService: UsersService;

  const mockUsersService = {
    findById: jest.fn(),
  };

  // Mock constructor for conversationModel
  const mockConversationModelStatics = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
    create: jest.fn(),
  };
  function MockConversationModelConstructor(dto) {
    return {
      ...dto,
      save: jest.fn().mockResolvedValue({ _id: 'conversation1', ...dto }),
    };
  }
  Object.assign(MockConversationModelConstructor, mockConversationModelStatics);

  // Mock constructor for messageModel
  const mockMessageModelStatics = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
    create: jest.fn(),
  };
  function MockMessageModelConstructor(dto) {
    return {
      ...dto,
      save: jest.fn().mockResolvedValue({ _id: 'message1', ...dto }),
    };
  }
  Object.assign(MockMessageModelConstructor, mockMessageModelStatics);

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MessagingService,
        {
          provide: getModelToken(Conversation.name),
          useValue: MockConversationModelConstructor as any,
        },
        {
          provide: getModelToken(Message.name),
          useValue: MockMessageModelConstructor as any,
        },
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
      ],
    }).compile();

    service = module.get<MessagingService>(MessagingService);
    conversationModel = module.get<Model<Conversation>>(getModelToken(Conversation.name));
    messageModel = module.get<Model<Message>>(getModelToken(Message.name));
    usersService = module.get<UsersService>(UsersService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createConversation', () => {
    it('should create a new conversation', async () => {
      const participants = ['user1', 'user2'];
      const title = 'Test Conversation';
      const isGroup = true;

      // Mock the users service to validate participants
      mockUsersService.findById.mockResolvedValue({ _id: 'user1' });

      const savedConversation = {
        _id: 'conversation1',
        participants,
        title,
        isGroup,
        // Remove lastMessageAt from expected result, as service does not set it
      };

      const result = await service.createConversation(participants, title, isGroup);

      expect(mockUsersService.findById).toHaveBeenCalledTimes(2);
      expect(result).toEqual(savedConversation);
    });
  });

  describe('findConversationById', () => {
    it('should return a conversation when it exists', async () => {
      const mockConversation = { _id: 'conversation1', participants: ['user1', 'user2'] };

      (conversationModel as any).findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockConversation),
      });

      const result = await service.findConversationById('conversation1');

      expect(conversationModel.findById).toHaveBeenCalledWith('conversation1');
      expect(result).toEqual(mockConversation);
    });

    it('should throw NotFoundException when conversation does not exist', async () => {
      (conversationModel as any).findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.findConversationById('nonexistent')).rejects.toThrow(NotFoundException);
      expect(conversationModel.findById).toHaveBeenCalledWith('nonexistent');
    });
  });

  describe('findConversationsByUserId', () => {
    it('should return conversations for a user', async () => {
      const mockConversations = [
        { _id: 'conversation1', participants: ['user1', 'user2'] },
        { _id: 'conversation2', participants: ['user1', 'user3'] },
      ];

      (conversationModel as any).find.mockReturnValue({
        sort: jest.fn().mockReturnValue({
          exec: jest.fn().mockResolvedValue(mockConversations),
        }),
      });

      const result = await service.findConversationsByUserId('user1');

      expect(conversationModel.find).toHaveBeenCalledWith({ participants: 'user1' });
      expect(result).toEqual(mockConversations);
    });
  });

  describe('createMessage', () => {
    it('should create a new message and update conversation lastMessageAt', async () => {
      const conversationId = 'conversation1';
      const senderId = 'user1';
      const content = 'Hello!';

      const mockConversation = { _id: conversationId, participants: [senderId, 'user2'] };
      const mockUser = { _id: senderId, email: '<EMAIL>' };

      // Mock findConversationById
      jest.spyOn(service, 'findConversationById').mockResolvedValue(mockConversation as any);

      // Mock usersService.findById
      mockUsersService.findById.mockResolvedValue(mockUser);

      const savedMessage = {
        _id: 'message1',
        conversationId,
        senderId,
        content,
        // Remove isRead and readAt from expected result, as service does not set them on creation
      };

      (conversationModel as any).findByIdAndUpdate.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockConversation),
      });

      const result = await service.createMessage(conversationId, senderId, content);

      expect(service.findConversationById).toHaveBeenCalledWith(conversationId);
      expect(mockUsersService.findById).toHaveBeenCalledWith(senderId);
      expect((conversationModel as any).findByIdAndUpdate).toHaveBeenCalledWith(
        conversationId,
        { lastMessageAt: expect.any(Date) },
      );
      expect(result).toEqual(savedMessage);
    });
  });

  describe('findMessagesByConversationId', () => {
    it('should return messages for a conversation', async () => {
      const conversationId = 'conversation1';
      const mockMessages = [
        { _id: 'message1', conversationId, content: 'Hello!' },
        { _id: 'message2', conversationId, content: 'Hi there!' },
      ];

      (messageModel as any).find.mockReturnValue({
        sort: jest.fn().mockReturnValue({
          exec: jest.fn().mockResolvedValue(mockMessages),
        }),
      });

      const result = await service.findMessagesByConversationId(conversationId);

      expect(messageModel.find).toHaveBeenCalledWith({ conversationId });
      expect(result).toEqual(mockMessages);
    });
  });

  describe('markMessageAsRead', () => {
    it('should mark a message as read when user is not the sender', async () => {
      const messageId = 'message1';
      const userId = 'user2';

      const mockMessage = {
        _id: messageId,
        senderId: 'user1',
        isRead: false,
        readAt: null,
        save: jest.fn().mockResolvedValue({
          _id: messageId,
          senderId: 'user1',
          isRead: true,
          readAt: new Date(),
        }),
      };

      (messageModel as any).findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockMessage),
      });

      const result = await service.markMessageAsRead(messageId, userId);

      expect(messageModel.findById).toHaveBeenCalledWith(messageId);
      expect(mockMessage.save).toHaveBeenCalled();
      expect(result.isRead).toBe(true);
      expect(result.readAt).toBeInstanceOf(Date);
    });

    it('should not mark a message as read when user is the sender', async () => {
      const messageId = 'message1';
      const userId = 'user1';

      const mockMessage = {
        _id: messageId,
        senderId: userId,
        isRead: false,
        readAt: null,
        save: jest.fn(),
      };

      (messageModel as any).findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockMessage),
      });

      const result = await service.markMessageAsRead(messageId, userId);

      expect(messageModel.findById).toHaveBeenCalledWith(messageId);
      expect(mockMessage.save).not.toHaveBeenCalled();
      expect(result).toEqual(mockMessage);
    });

    it('should throw NotFoundException when message does not exist', async () => {
      (messageModel as any).findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.markMessageAsRead('nonexistent', 'user1')).rejects.toThrow(NotFoundException);
      expect(messageModel.findById).toHaveBeenCalledWith('nonexistent');
    });
  });
});
