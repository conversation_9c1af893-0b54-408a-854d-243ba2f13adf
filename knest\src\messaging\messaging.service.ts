import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ObjectId } from 'mongoose';
import { Conversation, ConversationDocument } from './schemas/conversation.schema';
import { Message, MessageDocument } from './schemas/message.schema';
import { UsersService } from '../users/users.service';

@Injectable()
export class MessagingService {
  constructor(
    @InjectModel(Conversation.name) private conversationModel: Model<ConversationDocument>,
    @InjectModel(Message.name) private messageModel: Model<MessageDocument>,
    private usersService: UsersService,
  ) {}

  async createConversation(participants: string[], title?: string, isGroup = false): Promise<ConversationDocument> {
    // Validate participants exist
    for (const userId of participants) {
      await this.usersService.findById(userId);
    }

    const conversation = new this.conversationModel({
      participants,
      title: title || null,
      isGroup,
    });

    return conversation.save();
  }

  async findConversationById(id: string): Promise<ConversationDocument> {
    const conversation = await this.conversationModel.findById(id).exec();
    if (!conversation) {
      throw new NotFoundException(`Conversation with ID ${id} not found`);
    }
    return conversation;
  }

  async findConversationsByUserId(userId: string): Promise<ConversationDocument[]> {
    return this.conversationModel
      .find({ participants: userId })
      .sort({ lastMessageAt: -1 })
      .exec();
  }

  async createMessage(conversationId: string, senderId: string, content: string): Promise<MessageDocument> {
    // Validate conversation exists
    await this.findConversationById(conversationId);
    
    // Validate sender exists
    await this.usersService.findById(senderId);

    // Create message
    const message = new this.messageModel({
      conversationId,
      senderId,
      content,
    });

    // Update conversation's lastMessageAt
    await this.conversationModel.findByIdAndUpdate(
      conversationId,
      { lastMessageAt: new Date() },
    ).exec();

    return message.save();
  }

  async findMessagesByConversationId(conversationId: string): Promise<MessageDocument[]> {
    return this.messageModel
      .find({ conversationId })
      .sort({ createdAt: 1 })
      .exec();
  }

  async markMessageAsRead(messageId: string, userId: string): Promise<MessageDocument> {
    const message = await this.messageModel.findById(messageId).exec();
    
    if (!message) {
      throw new NotFoundException(`Message with ID ${messageId} not found`);
    }

    // Only mark as read if the user is not the sender
    if (message.senderId.toString() !== userId) {
      message.isRead = true;
      message.readAt = new Date();
      return message.save();
    }

    return message;
  }
}
