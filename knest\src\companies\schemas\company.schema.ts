import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export enum CompanyAccountType {
  FREE = 0,
  PAID = 1,
  COMP = 2,
  PREMIUM = 3,
}

export type CompanyDocument = Company & Document;

@Schema({
  collection: 'kd_companies',
  timestamps: true,
})
export class Company {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true, unique: true, index: true })
  invite_code: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true })
  createdBy: MongooseSchema.Types.ObjectId;

  @Prop({ type: Number, default: CompanyAccountType.FREE })
  accountType: CompanyAccountType;

  @Prop({ required: false })
  logo: string;

  @Prop({ type: Object, required: false })
  transformedLogo: {
    small: string;
    medium: string;
    large: string;
  };

  @Prop({ type: [String], default: [] })
  hashtags: string[];

  @Prop({ type: Object, default: {} })
  settings: Record<string, any>;
}

export const CompanySchema = SchemaFactory.createForClass(Company);
