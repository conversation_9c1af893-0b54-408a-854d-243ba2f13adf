import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type PostTypeDocument = PostType & Document;

@Schema({
  collection: 'kd_post_types',
  timestamps: true,
})
export class PostType {
  @Prop({ required: true })
  name: string;

  @Prop({ required: false })
  description: string;

  @Prop({ required: false })
  recordTips: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Company', required: true, index: true })
  companyId: MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true })
  userId: MongooseSchema.Types.ObjectId;

  @Prop({ type: Boolean, default: false })
  archived: boolean;

  @Prop({ required: false })
  highlightColor: string;
}

export const PostTypeSchema = SchemaFactory.createForClass(PostType);
